import { useState, useRef } from 'react';
import { cn } from '@/lib/utils';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ImageCropper } from '@/components/image/ImageCropper';
import { Upload } from 'lucide-react';
import toast from 'react-hot-toast';
import type { ImageSettings } from '@/types/tools';

interface ImageUploaderProps extends React.HTMLAttributes<HTMLDivElement> {
    currentImage?: string;
    onUpload: (file: File) => Promise<string | null>;
    onImageSelected?: (url: string) => void;
    label?: string;
}

export function ImageUploader({
    currentImage,
    onUpload,
    className,
    ...props
}: ImageUploaderProps) {
    const [isDragging, setIsDragging] = useState(false);
    const [showCropper, setShowCropper] = useState(false);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const handleDrop = async (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);

        const file = e.dataTransfer.files[0];
        if (file && file.type.startsWith('image/')) {
            setSelectedFile(file);
            setShowCropper(true);
        } else {
            toast.error('Please upload an image file');
        }
    };

    const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file && file.type.startsWith('image/')) {
            setSelectedFile(file);
            setShowCropper(true);
        } else {
            toast.error('Please upload an image file');
        }
    };

    const handleCropComplete = async (originalUrl: string, settings: ImageSettings) => {
        setShowCropper(false);
        try {
            const response = await fetch(originalUrl);
            const blob = await response.blob();
            const file = new File([blob], 'cropped-image.png', { type: 'image/png' });

            const uploadedUrl = await onUpload(file);
            if (uploadedUrl && props.onImageSelected) {
                props.onImageSelected(uploadedUrl);
            }
        } catch (error) {
            console.error('Error handling cropped image:', error);
            toast.error('Failed to process image');
        }
    };

    return (
        <>
            <div
                className={cn(
                    'relative h-[200px] rounded-lg border-2 border-dashed transition-colors',
                    isDragging ? 'border-sortmy-blue bg-sortmy-blue/5' : 'border-gray-300',
                    className
                )}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                {...props}
            >
                {currentImage ? (
                    <div className="relative h-full">
                        <img
                            src={currentImage}
                            alt="Uploaded"
                            className="h-full w-full object-contain rounded-lg"
                        />
                        <Button
                            onClick={() => fileInputRef.current?.click()}
                            className="absolute bottom-4 right-4"
                            size="sm"
                        >
                            Change
                        </Button>
                    </div>
                ) : (
                    <div className="flex h-full flex-col items-center justify-center">
                        <Upload className="h-8 w-8 text-gray-400" />
                        <p className="mt-2 text-sm text-gray-600">
                            Drag and drop an image, or{' '}
                            <button
                                type="button"
                                className="text-sortmy-blue hover:underline"
                                onClick={() => fileInputRef.current?.click()}
                            >
                                browse
                            </button>
                        </p>
                    </div>
                )}

                <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleFileSelect}
                />
            </div>

            <Dialog open={showCropper} onOpenChange={setShowCropper}>
                <DialogContent className="max-w-[800px] p-0">
                    <DialogHeader className="p-6 pb-0">
                        <DialogTitle>Crop Image</DialogTitle>
                        <DialogDescription>
                            Adjust the image to fit the required dimensions
                        </DialogDescription>
                    </DialogHeader>
                    {selectedFile && (
                        <ImageCropper
                            imageFile={selectedFile}
                            onComplete={handleCropComplete}
                        />
                    )}
                </DialogContent>
            </Dialog>
        </>
    );
}

