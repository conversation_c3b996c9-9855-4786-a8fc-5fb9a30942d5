import { useState, useEffect, useRef } from 'react';
import Hls from 'hls.js';

interface LivepeerPlayerProps {
    src?: string;
    playbackId?: string;
    poster?: string;
    title?: string;
    muted?: boolean;
    autoPlay?: boolean;
    loop?: boolean;
    className?: string;
    onPlay?: () => void;
    onPause?: () => void;
}

export function LivepeerPlayer({
    src,
    playbackId,
    poster,
    title,
    muted = true,
    autoPlay = false,
    loop = true,
    className = '',
    onPlay,
    onPause,
}: LivepeerPlayerProps) {
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const videoRef = useRef<HTMLVideoElement>(null);
    const hlsRef = useRef<Hls | null>(null);
    const retryTimeoutRef = useRef<NodeJS.Timeout>();

    // Cleanup function
    useEffect(() => {
        return () => {
            if (hlsRef.current) {
                hlsRef.current.destroy();
                hlsRef.current = null;
            }
            if (retryTimeoutRef.current) {
                clearTimeout(retryTimeoutRef.current);
            }
        };
    }, []);

    // Check if CDN is ready
    const checkCdnReady = async (url: string): Promise<boolean> => {
        try {
            const res = await fetch(url, { method: 'HEAD' });
            return res.ok;
        } catch {
            return false;
        }
    };

    useEffect(() => {
        let isMounted = true;

        const setupVideo = async () => {
            const video = videoRef.current;
            if (!video || !isMounted) return;

            // Clean up previous HLS instance
            if (hlsRef.current) {
                hlsRef.current.destroy();
                hlsRef.current = null;
            }

            try {
                // Get the playback URL - prefer playbackId over src
                const videoUrl = playbackId
                    ? `https://livepeercdn.com/hls/${playbackId}/index.m3u8`
                    : src;

                if (!videoUrl) {
                    setError('No playback source available');
                    setIsLoading(false);
                    return;
                }

                // Check if the CDN is ready
                const isReady = await checkCdnReady(videoUrl);
                if (!isReady && isMounted) {
                    setError('Video is preparing for playback. Please try again in a moment.');
                    setIsLoading(false);

                    // Retry after 5 seconds
                    retryTimeoutRef.current = setTimeout(() => {
                        if (isMounted) {
                            setIsLoading(true);
                            setError(null);
                            setupVideo();
                        }
                    }, 5000);
                    return;
                }

                if (!isMounted) return;

                // Use HLS.js if supported and we have an m3u8 URL
                if (Hls.isSupported() && videoUrl.includes('.m3u8')) {
                    const hls = new Hls({
                        enableWorker: true,
                        lowLatencyMode: true,
                        backBufferLength: 90,
                        maxBufferLength: 30,
                        maxMaxBufferLength: 60,
                        liveSyncDurationCount: 3,
                        liveMaxLatencyDurationCount: 10,
                        maxBufferHole: 0.5,
                        highBufferWatchdogPeriod: 2,
                        nudgeOffset: 0.1,
                        nudgeMaxRetry: 5
                    });

                    hls.on(Hls.Events.ERROR, (event, data) => {
                        console.error('HLS Error:', event, data);
                        if (data.fatal && isMounted) {
                            switch (data.type) {
                                case Hls.ErrorTypes.NETWORK_ERROR:
                                    console.log('Fatal network error encountered, trying to recover');
                                    hls.startLoad();
                                    break;
                                case Hls.ErrorTypes.MEDIA_ERROR:
                                    console.log('Fatal media error encountered, trying to recover');
                                    hls.recoverMediaError();
                                    break;
                                default:
                                    setError('Failed to load video stream. Please try again.');
                                    break;
                            }
                        }
                    });

                    hls.on(Hls.Events.MANIFEST_PARSED, () => {
                        if (isMounted) {
                            setIsLoading(false);
                            if (autoPlay) {
                                video.play().catch(console.error);
                            }
                        }
                    });

                    hls.loadSource(videoUrl);
                    hls.attachMedia(video);
                    hlsRef.current = hls;
                } else {
                    // Direct playback for non-HLS sources
                    video.src = videoUrl;
                    setIsLoading(false);
                    if (autoPlay) {
                        video.play().catch(console.error);
                    }
                }
            } catch (err) {
                console.error('Error setting up video:', err);
                if (isMounted) {
                    setError('Failed to initialize video player. Please try again.');
                    setIsLoading(false);
                }
            }
        };

        setupVideo();

        return () => {
            isMounted = false;
        };
    }, [src, playbackId, autoPlay]);

    if (error) {
        return (
            <div className="flex flex-col items-center justify-center w-full h-full bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
                <p className="text-red-500 dark:text-red-400 text-center">{error}</p>
                {isLoading && (
                    <div className="mt-2 text-sm text-gray-500">Retrying...</div>
                )}
            </div>
        );
    }

    if (isLoading) {
        return (
            <div className="flex items-center justify-center w-full h-full bg-gray-100 dark:bg-gray-800 rounded-lg">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div>
        );
    }

    return (
        <div className={`relative w-full h-full ${className}`}>
            <video
                ref={videoRef}
                poster={poster}
                title={title}
                className="w-full h-full object-cover rounded-lg"
                controls
                playsInline
                muted={muted}
                loop={loop}
                onPlay={onPlay}
                onPause={onPause}
            />
        </div>
    );
}
