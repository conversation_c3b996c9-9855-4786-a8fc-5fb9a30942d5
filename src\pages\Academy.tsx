import { useState, useEffect } from "react";
import { Separator } from "@/components/ui/separator";
import TierSection from "@/components/academy/TierSection";
import GlassCard from "@/components/ui/GlassCard";
import NeonButton from "@/components/ui/NeonButton";
import HoverEffect from "@/components/ui/HoverEffect";
import AISuggestion from "@/components/ui/AISuggestion";
import { Brain } from "lucide-react";
import { Tier, Module } from "@/types/academy";
import ModuleView from "@/pages/ModuleView";
import { collection, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";

interface AITool {
  id: string;
  name: string;
  description: string;
  category: string;
  excelsAt: string;
  freeCredits: string;
  logoLink: string;
}

const Academy = () => {
  const [selectedModule, setSelectedModule] = useState<Module | null>(null);  // Track only the selected module state

  const [tiers, setTiers] = useState<Tier[]>([
    {
      id: "tier1",
      name: "Essential AI Tools",
      isUnlocked: true,
      modules: [
        {
          id: "module1",
          title: "Turn Any Idea Into Viral Content with ChatGPT",
          description: "💡 Master prompt engineering to write scripts, captions, blogs & ads that go viral in minutes.",
          xpReward: 50,
          isCompleted: false,
          videoId: "chat-fundamentals",
          imageUrl: "/images/academy/chatgpt.webp"
        },
        {
          id: "module2",
          title: "Build Real Apps 10x Faster with GitHub Copilot",
          description: "👨‍💻 Let AI autocomplete your code, fix bugs, and ship faster than ever.",
          xpReward: 75,
          isCompleted: false,
          videoId: "copilot-basics",
          imageUrl: "/images/academy/github-copilot.webp"
        },
        {
          id: "module3",
          title: "Make VS Code Work Like a CTO's Brain",
          description: "💻 Customize your dev environment to write, test, and deploy like a startup founder.",
          xpReward: 100,
          isCompleted: false,
          videoId: "vscode-mastery",
          imageUrl: "/images/academy/vscode.webp"
        }
      ]
    },
    {
      id: "tier2",
      name: "Video & Content Creation",
      isUnlocked: true,
      modules: [
        {
          id: "module4",
          title: "Create Talking AI Influencers in 5 Clicks with HeyGen",
          description: "🗣 Make faceless avatar videos for your brand, agency, or client work using just text.",
          xpReward: 125,
          isCompleted: false,
          videoId: "heygen-intro",
          imageUrl: "/images/academy/heygen.jpg"
        },
        {
          id: "module5",
          title: "Edit YouTube Shorts & Reels in Minutes with Wondershare",
          description: "🎞 Cut, trim, subtitle & export viral videos — without needing a single tutorial.",
          xpReward: 150,
          isCompleted: false,
          videoId: "wondershare-basics",
          imageUrl: "/images/academy/wondershare.jpg"
        },
        {
          id: "module6",
          title: "Turn Any Script Into a Stunning Video with Sora",
          description: "🎬 Generate cinematic ads, storytelling reels & branded content—without touching a camera.",
          xpReward: 200,
          isCompleted: false,
          videoId: "sora-fundamentals",
          imageUrl: "/images/academy/sora.png"
        }
      ]
    },
    {
      id: "tier3",
      name: "Development & Deployment",
      isUnlocked: true,
      modules: [
        {
          id: "module7",
          title: "Deploy Your Website in 10 Minutes with Netlify",
          description: "🚀 From GitHub to live link — one-click hosting, no backend setup required.",
          xpReward: 150,
          isCompleted: false,
          videoId: "netlify-deploy",
          imageUrl: "/images/academy/netlify.png"
        },
        {
          id: "module8",
          title: "Build a High-Converting Website in Minutes with Lovable.ai",
          description: "🖥 No code. No designer. Just answer a few questions and get a full brand + website built by AI.",
          xpReward: 175,
          isCompleted: false,
          videoId: "lovable-dev",
          imageUrl: "/images/academy/lovable.webp"
        }
      ]
    },
    {
      id: "tier4",
      name: "Automation & Integration",
      isUnlocked: true,
      modules: [
        {
          id: "module9",
          title: "Automate Your Entire Business Without Code Using Zapier",
          description: "🔗 Connect your forms, CRMs, Notion, Sheets, and more — in under 1 hour.",
          xpReward: 150,
          isCompleted: false,
          videoId: "zapier-auto",
          imageUrl: "/images/academy/zapier.webp"
        },
        {
          id: "module10",
          title: "Build Advanced Workflows Without Limits Using N8N",
          description: "🧠 Create smart, open-source automations Zapier can't handle — no paywalls, full control.",
          xpReward: 200,
          isCompleted: false,
          videoId: "n8n-workflows",
          imageUrl: "/images/academy/n8n.webp"
        }
      ]
    },
    {
      id: "tier5",
      name: "Chat & Communication",
      isUnlocked: true,
      modules: [
        {
          id: "module11",
          title: "Set Up DM Funnels That Sell Using ManyChat",
          description: "📲 Automate Instagram, WhatsApp & Messenger replies to collect leads and close deals on autopilot.",
          xpReward: 125,
          isCompleted: false,
          videoId: "manychats-setup",
          imageUrl: "/images/academy/manychat.png"
        },
        {
          id: "module12",
          title: "Automate Customer Replies, Emails & DMs Using ChatGPT",
          description: "⚙️ Use AI to talk to leads, write follow-ups, and answer questions — 24/7.",
          xpReward: 125,
          isCompleted: false,
          videoId: "chatgpt-automation",
          imageUrl: "/images/academy/chatgpt.webp"
        }
      ]
    }
  ]);

  // Fetch AI tools and convert them to a course tier
  useEffect(() => {
    const fetchAiTools = async () => {
      try {
        const toolsCollection = collection(db, 'aiTools'); // Changed from 'ai-tools' to 'aiTools'
        const toolsSnapshot = await getDocs(toolsCollection);
        const toolsList: AITool[] = [];

        toolsSnapshot.forEach((doc) => {
          const tool = {
            id: doc.id,
            ...doc.data()
          } as AITool;
          toolsList.push(tool);
        });

        // Group tools by category
        const toolsByCategory = toolsList.reduce((acc, tool) => {
          const category = tool.category || 'Other';
          if (!acc[category]) {
            acc[category] = [];
          }
          acc[category].push(tool);
          return acc;
        }, {} as Record<string, AITool[]>);

        // Create new tiers from AI tools
        const aiToolTiers = Object.entries(toolsByCategory).map(([category, tools], index) => ({
          id: `ai-tier-${index}`,
          name: category,
          isUnlocked: true,
          modules: tools.map(tool => ({
            id: tool.id,
            title: tool.name,
            description: tool.description || '',
            xpReward: 50,
            isCompleted: false,
            videoId: tool.id,
            imageUrl: tool.logoLink,
            excelsAt: tool.excelsAt,
            freeCredits: tool.freeCredits
          }))
        }));        // Put hardcoded tiers first, followed by AI tool tiers
        setTiers(prev => [...prev, ...aiToolTiers]);
      } catch (error) {
        console.error('Error fetching AI tools:', error);
      }
    };

    fetchAiTools();
  }, []);

  const handleStartModule = (moduleId: string) => {
    // Find the module in all tiers
    const module = tiers.flatMap(tier => tier.modules)
      .find(m => m.id === moduleId);

    if (module) {
      setSelectedModule(module);
      console.log("Selected module:", module);
    }
  };

  const handleBackToAcademy = () => {
    setSelectedModule(null);
  };

  // If a module is selected, show the module view
  if (selectedModule) {
    return <ModuleView module={selectedModule} onBack={handleBackToAcademy} />;
  }

  // Otherwise show the academy overview
  return (
    <div className="space-y-6 p-1 md:p-4">
      <div className="flex flex-col gap-2">
        <h1 className="text-2xl font-bold">SortMyAI Academy</h1>
        <p className="text-gray-400">
          Master AI skills and earn XP through structured learning paths
        </p>
      </div>

      <GlassCard variant="bordered" className="border-sortmy-blue/20">
        <div className="p-4">
          <div className="flex items-center mb-2">
            <Brain className="w-5 h-5 mr-2 text-sortmy-blue" />
            <h2 className="text-lg font-semibold">AI Learning Path</h2>
          </div>
          <p className="text-sm text-gray-300 mb-4">
            Complete modules to earn XP and unlock advanced content. Track your progress and build your AI skills systematically.
          </p>
          <HoverEffect effect="lift" color="blue">
            <NeonButton variant="gradient" size="sm" onClick={() => console.log('View learning path')}>
              View Learning Path
            </NeonButton>
          </HoverEffect>
        </div>
      </GlassCard>

      {/* AI Tools Collection Section */}      <div className="mb-8">
        <AISuggestion
          suggestion="Start with ChatGPT Fundamentals to build a strong foundation in AI tools. Then explore platform-specific courses based on your interests in content creation, development, or automation."
          actionText="Start Learning"
          onAction={() => handleStartModule('module1')}
        />
      </div>

      <Separator className="bg-sortmy-blue/20" />

      <div className="space-y-8">
        {tiers.map((tier) => (
          <TierSection
            key={tier.id}
            tier={tier}
            onStartModule={handleStartModule}
          />
        ))}
      </div>
    </div>
  );
};

export default Academy;
