/**
 * Utility functions for handling Google Drive URLs and file IDs
 */

/**
 * Extract Google Drive file ID from various URL formats
 */
export const getGoogleDriveFileId = (url: string): string | null => {
  if (!url || !url.includes('drive.google.com')) {
    return null;
  }

  // Handle different Google Drive URL formats
  const patterns = [
    /\/d\/([a-zA-Z0-9_-]+)/,           // /d/FILE_ID format
    /id=([a-zA-Z0-9_-]+)/,            // id=FILE_ID format
    /file\/d\/([a-zA-Z0-9_-]+)/,      // file/d/FILE_ID format
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
};

/**
 * Generate optimized Google Drive image URLs with fallbacks
 */
export const getGoogleDriveImageUrls = (fileId: string) => {
  return {
    // Primary URL - works best for most images
    primary: `https://lh3.googleusercontent.com/d/${fileId}`,
    // Fallback URL - thumbnail format, more reliable but lower quality
    fallback: `https://drive.google.com/thumbnail?id=${fileId}&sz=w1000`,
    // View URL - for opening in Google Drive
    view: `https://drive.google.com/file/d/${fileId}/view`,
    // Preview URL - for embedding videos
    preview: `https://drive.google.com/file/d/${fileId}/preview`,
  };
};

/**
 * Check if a URL is a Google Drive URL
 */
export const isGoogleDriveUrl = (url: string): boolean => {
  return url.includes('drive.google.com') || url.includes('googleusercontent.com');
};

/**
 * Get the best image URL for a Google Drive file
 */
export const getBestGoogleDriveImageUrl = (url: string): string | null => {
  const fileId = getGoogleDriveFileId(url);
  if (!fileId) return null;

  const urls = getGoogleDriveImageUrls(fileId);
  return urls.primary;
};

/**
 * Get fallback image URL for a Google Drive file
 */
export const getFallbackGoogleDriveImageUrl = (url: string): string | null => {
  const fileId = getGoogleDriveFileId(url);
  if (!fileId) return null;

  const urls = getGoogleDriveImageUrls(fileId);
  return urls.fallback;
};

/**
 * Create a CORS-friendly proxy URL for development
 */
export const getProxyUrl = (url: string): string => {
  // In development, use the proxy we set up in vite.config.ts
  if (import.meta.env.DEV && isGoogleDriveUrl(url)) {
    const fileId = getGoogleDriveFileId(url);
    if (fileId) {
      return `/google-drive-proxy/thumbnail?id=${fileId}&sz=w1000`;
    }
  }
  return url;
};

/**
 * Handle Google Drive image loading with proper error handling
 */
export const createGoogleDriveImageElement = (
  fileId: string,
  alt: string,
  className: string,
  onError?: () => void
): HTMLImageElement => {
  const img = new Image();
  const urls = getGoogleDriveImageUrls(fileId);
  
  img.alt = alt;
  img.className = className;
  img.crossOrigin = 'anonymous';
  img.referrerPolicy = 'no-referrer';
  
  // Set up error handling with fallback
  img.onerror = () => {
    // Try fallback URL
    if (img.src === urls.primary) {
      img.src = urls.fallback;
    } else if (onError) {
      // If fallback also fails, call the error handler
      onError();
    }
  };
  
  // Start with primary URL
  img.src = urls.primary;
  
  return img;
};
