import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Search, 
  Package, 
  Check, 
  X,
  Loader2,
  Tag,
  Calendar
} from 'lucide-react';
import { Toolkit } from '@/types/tools';
import { useAuth } from '@/hooks/useAuth';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface ToolkitSelectorProps {
  selectedToolkits: string[];
  onSelectionChange: (toolkitIds: string[]) => void;
  maxSelection?: number;
}

export const ToolkitSelector: React.FC<ToolkitSelectorProps> = ({
  selectedToolkits,
  onSelectionChange,
  maxSelection = 5
}) => {
  const { user } = useAuth();
  const [toolkits, setToolkits] = useState<Toolkit[]>([]);
  const [filteredToolkits, setFilteredToolkits] = useState<Toolkit[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    if (user) {
      fetchUserToolkits();
    }
  }, [user]);

  useEffect(() => {
    // Filter toolkits based on search query
    if (searchQuery.trim() === '') {
      setFilteredToolkits(toolkits);
    } else {
      const filtered = toolkits.filter(toolkit =>
        toolkit.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        toolkit.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        toolkit.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredToolkits(filtered);
    }
  }, [searchQuery, toolkits]);

  const fetchUserToolkits = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const toolkitsRef = collection(db, 'toolkits');
      const q = query(
        toolkitsRef, 
        where('created_by', '==', user.uid)
      );
      const snapshot = await getDocs(q);
      
      const userToolkits = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Toolkit[];
      
      setToolkits(userToolkits);
      setFilteredToolkits(userToolkits);
    } catch (error) {
      console.error('Error fetching user toolkits:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToolkitToggle = (toolkitId: string) => {
    if (selectedToolkits.includes(toolkitId)) {
      // Remove from selection
      onSelectionChange(selectedToolkits.filter(id => id !== toolkitId));
    } else {
      // Add to selection (if under max limit)
      if (selectedToolkits.length < maxSelection) {
        onSelectionChange([...selectedToolkits, toolkitId]);
      }
    }
  };

  const clearSelection = () => {
    onSelectionChange([]);
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Unknown';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Tag Toolkits to Project
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="w-5 h-5" />
          Tag Toolkits to Project
        </CardTitle>
        <p className="text-sm text-gray-600">
          Select up to {maxSelection} toolkits to associate with this project
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Search your toolkits..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Selection Summary */}
        {selectedToolkits.length > 0 && (
          <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {selectedToolkits.length} of {maxSelection} selected
              </Badge>
              {selectedToolkits.length === maxSelection && (
                <span className="text-sm text-orange-600">Maximum reached</span>
              )}
            </div>
            <Button
              onClick={clearSelection}
              variant="outline"
              size="sm"
            >
              <X className="w-4 h-4 mr-1" />
              Clear
            </Button>
          </div>
        )}

        {/* Toolkits List */}
        {filteredToolkits.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {toolkits.length === 0 ? (
              <div>
                <Package className="w-12 h-12 mx-auto mb-3 opacity-30" />
                <p>You haven't created any toolkits yet</p>
                <p className="text-sm">Create a toolkit first to tag it to projects</p>
              </div>
            ) : (
              <div>
                <Search className="w-12 h-12 mx-auto mb-3 opacity-30" />
                <p>No toolkits match your search</p>
              </div>
            )}
          </div>
        ) : (
          <ScrollArea className="h-64">
            <div className="space-y-2">
              {filteredToolkits.map((toolkit) => {
                const isSelected = selectedToolkits.includes(toolkit.id);
                const canSelect = isSelected || selectedToolkits.length < maxSelection;
                
                return (
                  <div
                    key={toolkit.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-all ${
                      isSelected 
                        ? 'border-blue-500 bg-blue-50' 
                        : canSelect
                          ? 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                          : 'border-gray-100 bg-gray-50 cursor-not-allowed opacity-50'
                    }`}
                    onClick={() => canSelect && handleToolkitToggle(toolkit.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium">{toolkit.name}</h4>
                          {isSelected && (
                            <Check className="w-4 h-4 text-blue-600" />
                          )}
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          {toolkit.description}
                        </p>
                        
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span className="flex items-center gap-1">
                            <Package className="w-3 h-3" />
                            {toolkit.tools?.length || 0} tools
                          </span>
                          <span className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {formatDate(toolkit.created_at)}
                          </span>
                        </div>
                        
                        {toolkit.tags && toolkit.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {toolkit.tags.slice(0, 3).map((tag, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {toolkit.tags.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{toolkit.tags.length - 3}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        )}

        {/* Help Text */}
        <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
          <div className="flex items-start gap-2">
            <Tag className="w-4 h-4 mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium mb-1">About Toolkit Tagging:</p>
              <p>
                Tagging toolkits to your project helps showcase the tools you used and makes 
                your work more discoverable. Tagged toolkits will appear in your project details.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
