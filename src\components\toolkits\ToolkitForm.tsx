import { useState, useEffect, useMemo } from 'react';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { toast } from 'react-hot-toast';
import { db, storage } from '@/lib/firebase';
import type { Tool, Toolkit, ToolkitFormData } from '@/types/tools';
import type { AITool } from '@/types';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { TagInput } from '@/components/ui/tag-input';
import { ImageUploader } from '@/components/ui/image-uploader';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { ToolSelector } from './ToolSelector';
import { ensureStringArray } from '@/lib/array-utils';

interface ToolkitFormProps {
    initialData?: Toolkit;
    onSubmit: (data: ToolkitFormData) => Promise<void>;
    onCancel: () => void;
}

export function ToolkitForm({ initialData, onSubmit, onCancel }: ToolkitFormProps) {
    const { user } = useAuth();
    const [isLoading, setIsLoading] = useState(false);
    const [userTools, setUserTools] = useState<Tool[]>([]);
    const [libraryTools, setLibraryTools] = useState<Tool[]>([]);

    // Form state
    const [name, setName] = useState(initialData?.name ?? '');
    const [description, setDescription] = useState(initialData?.description ?? '');
    const [tags, setTags] = useState<string[]>(initialData?.tags ?? []);
    const [logoUrl, setLogoUrl] = useState<string>(initialData?.logoUrl ?? initialData?.logo_url ?? '');
    const [selectedTools, setSelectedTools] = useState<string[]>(initialData?.tools ?? []);

    // Fetch tools on mount
    useEffect(() => {
        const fetchTools = async () => {
            if (!user?.uid) return;
            setIsLoading(true);
            try {
                // Fetch user's tools
                const userToolsSnapshot = await getDocs(query(
                    collection(db, 'tools'),
                    where('user_id', '==', user.uid)
                ));
                const userToolsData = userToolsSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                } as Tool));

                // Fetch library tools
                const libraryToolsSnapshot = await getDocs(query(
                    collection(db, 'tools'),
                    where('is_library', '==', true)
                ));
                const libraryToolsData = libraryToolsSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                } as Tool));

                // Fetch AI tools
                let aiToolsData: Tool[] = [];
                try {
                    const aiToolsSnapshot = await getDocs(collection(db, 'aiTools'));

                    aiToolsData = aiToolsSnapshot.docs.map(doc => {
                        const data = doc.data() as AITool;
                        // Transform AITool to Tool interface
                        return {
                            id: doc.id,
                            name: data.name,
                            description: data.description,
                            logo_url: data.logoUrl, // Map logoUrl to logo_url
                            logoUrl: data.logoUrl, // Keep both for compatibility
                            website: data.website,
                            tags: Array.isArray(data.tags) ? data.tags :
                                typeof data.tags === 'string' ? data.tags.split(',').map((tag: string) => tag.trim()) : [],
                            category: data.category,
                            created_at: data.createdAt || new Date().toISOString(),
                            updated_at: data.updatedAt,
                            user_id: data.createdBy,
                            pricing: data.pricing,
                            useCase: data.useCase,
                            excelsAt: data.excelsAt,
                            source: 'library' as const,
                            image_settings: data.imageSettings
                        } as Tool;
                    });
                } catch (aiToolsError) {
                    console.error('Error fetching AI tools:', aiToolsError);
                    // Continue without AI tools if there's an error
                }

                setUserTools(userToolsData);
                setLibraryTools([...libraryToolsData, ...aiToolsData]);
            } catch (error) {
                console.error('Error fetching tools:', error);
                toast.error('Failed to load tools');
            } finally {
                setIsLoading(false);
            }
        };

        fetchTools();
    }, [user?.uid]);

    // Combine and deduplicate tools
    const allTools = useMemo(() => {
        const toolMap = new Map();
        [...userTools, ...libraryTools].forEach(tool => {
            if (!toolMap.has(tool.id)) {
                toolMap.set(tool.id, tool);
            }
        });
        return Array.from(toolMap.values());
    }, [userTools, libraryTools]);



    const handleImageUpload = async (file: File): Promise<string | null> => {
        if (!user?.uid) return null;
        try {
            setIsLoading(true);
            const storageRef = ref(storage, `toolkit-logos/${user.uid}/${Date.now()}-${file.name}`);
            const uploadResult = await uploadBytes(storageRef, file);
            const url = await getDownloadURL(uploadResult.ref);
            setLogoUrl(url);
            return url;
        } catch (error) {
            console.error('Error uploading image:', error);
            toast.error('Failed to upload logo');
            return null;
        } finally {
            setIsLoading(false);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!user?.uid) return;

        if (!name.trim()) {
            toast.error('Name is required');
            return;
        }

        if (!logoUrl) {
            toast.error('Please upload a logo');
            return;
        }

        if (selectedTools.length === 0) {
            toast.error('Please select at least one tool');
            return;
        } const formData: ToolkitFormData = {
            name: name.trim(),
            description: description.trim(),
            logoUrl: logoUrl,
            logo_url: logoUrl, // Include both for backward compatibility
            tags: ensureStringArray(tags),
            tools: selectedTools,
            user_id: user.uid,
            is_published: false,
            source: 'user'
        };

        await onSubmit(formData);
    };

    if (isLoading) {
        return <div className="flex items-center justify-center p-8">
            <LoadingSpinner />
        </div>;
    }

    return (
        <form onSubmit={handleSubmit} className="space-y-6">
            <div>
                <Label htmlFor="logo">Logo</Label>
                <ImageUploader
                    currentImage={logoUrl}
                    onUpload={handleImageUpload}
                    onImageSelected={(url) => setLogoUrl(url)}
                    label="Upload Logo"
                    className="mt-2"
                />
            </div>

            <div>
                <Label htmlFor="name">Name</Label>
                <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="mt-2"
                />
            </div>

            <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="mt-2"
                    rows={4}
                />
            </div>

            <div>
                <Label>Tools</Label>
                <div className="mt-2">
                    <ToolSelector
                        availableTools={allTools}
                        selectedTools={selectedTools}
                        onChange={setSelectedTools}
                    />
                </div>
            </div>

            <div>
                <Label htmlFor="tags">Tags</Label>
                <TagInput
                    id="tags"
                    tags={tags}
                    onChange={setTags}
                    className="mt-2"
                />
            </div>

            <div className="flex justify-end gap-4">
                <Button
                    type="button"
                    variant="outline"
                    onClick={onCancel}
                >
                    Cancel
                </Button>
                <Button
                    type="submit"
                    disabled={isLoading}
                >
                    {initialData ? 'Update' : 'Create'} Toolkit
                </Button>
            </div>
        </form>
    );
}
