import { Tool, Toolkit } from '@/types/tools';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Briefcase, Edit, Trash, Share } from 'lucide-react';

interface ToolkitPreviewProps {
    toolkit: Toolkit;
    tools: Tool[];
    onEdit?: (e: React.MouseEvent, toolkit: Toolkit) => void;
    onDelete?: (e: React.MouseEvent, toolkit: Toolkit) => void;
    onShare?: (e: React.MouseEvent, toolkit: Toolkit) => void;
    isOwner?: boolean;
}

export const ToolkitPreview: React.FC<ToolkitPreviewProps> = ({
    toolkit,
    tools,
    onEdit,
    onDelete,
    onShare,
    isOwner
}) => {
    const toolIds = toolkit.tools || [];
    // Use a Map to deduplicate tools by ID
    const toolMap = new Map();
    tools.forEach(tool => {
        if (toolIds.includes(tool.id) && !toolMap.has(tool.id)) {
            toolMap.set(tool.id, tool);
        }
    });
    const toolkitTools = Array.from(toolMap.values());

    return (
        <Card className="p-4 bg-sortmy-darker border-sortmy-blue/20">
            <div className="flex items-center gap-4 mb-4">
                {toolkit.logo_url ? (
                    <img
                        src={toolkit.logo_url}
                        alt={toolkit.name}
                        className="w-12 h-12 rounded-lg object-cover"
                    />
                ) : (
                    <div className="w-12 h-12 rounded-lg bg-sortmy-blue/20 flex items-center justify-center">
                        <Briefcase className="w-6 h-6 text-sortmy-blue/60" />
                    </div>
                )}
                <div className="flex-1">
                    <h3 className="text-lg font-semibold">{toolkit.name}</h3>
                    <p className="text-sm text-gray-400">{toolkit.description}</p>
                </div>
                {isOwner && (
                    <div className="flex gap-2">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                onEdit?.(e, toolkit);
                            }}
                        >
                            <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                onDelete?.(e, toolkit);
                            }}
                        >
                            <Trash className="w-4 h-4" />
                        </Button>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                onShare?.(e, toolkit);
                            }}
                        >
                            <Share className="w-4 h-4" />
                        </Button>
                    </div>
                )}
            </div>

            <ScrollArea className="h-24">
                <div className="flex flex-wrap gap-2">
                    {toolkitTools.map(tool => (
                        <Badge
                            key={tool.id}
                            variant="outline"
                            className="bg-sortmy-blue/10"
                        >
                            {tool.name}
                        </Badge>
                    ))}
                </div>
            </ScrollArea>
        </Card>
    );
};
