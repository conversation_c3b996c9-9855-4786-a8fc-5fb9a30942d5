
import { useState, useEffect } from 'react';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Play } from 'lucide-react';

interface YoutubeShortEmbedProps {
  videoId: string;
  title?: string;
  onProgressChange?: (progress: number) => void;
}

const YoutubeShortEmbed = ({ videoId, title, onProgressChange }: YoutubeShortEmbedProps) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isShort, setIsShort] = useState(true);

  const embedUrl = `https://www.youtube.com/embed/${videoId}?playsinline=1&autoplay=0&rel=0`;
  const thumbnailUrl = `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`;
  const directUrl = `https://www.youtube.com/watch?v=${videoId}`;

  useEffect(() => {
    // For now, assume all videos are regular videos (16:9 aspect ratio)
    // We can't reliably detect shorts due to CSP restrictions
    setIsShort(false);
  }, [videoId]);

  const handleLoad = () => {
    setIsLoaded(true);
    // Simulate progress for demo purposes
    if (onProgressChange) {
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        onProgressChange(Math.min(progress, 100));
        if (progress >= 100) clearInterval(interval);
      }, 3000);
    }
  };

  const handleError = () => {
    setIsError(true);
  };

  if (isError) {
    return (
      <a
        href={directUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="block relative rounded-lg overflow-hidden group transition-all"
        aria-label={title ? `Watch YouTube video: ${title}` : 'Watch YouTube video'}
      >
        <img
          src={thumbnailUrl}
          alt={title || 'YouTube video thumbnail'}
          className="w-full h-auto transition-transform duration-300 group-hover:scale-105"
        />
        <div className="absolute inset-0 flex items-center justify-center bg-black/40 group-hover:bg-black/50 transition-colors">
          <Play size={48} className="text-white opacity-80 group-hover:opacity-100 transition-opacity" />
        </div>
      </a>
    );
  }

  return (
    <div className="relative rounded-lg overflow-hidden shadow-md">
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-sortmy-darker z-10">
          <div className="w-full">
            <img
              src={thumbnailUrl}
              alt={title || 'Loading YouTube video'}
              className="w-full h-auto opacity-70"
            />
            <div className="absolute inset-0 flex items-center justify-center bg-black/40">
              <div className="animate-pulse flex items-center gap-2">
                <Play size={24} className="text-white" />
                <span className="text-white text-sm font-medium">Loading...</span>
              </div>
            </div>
          </div>
        </div>
      )}

      <AspectRatio ratio={isShort ? 9 / 16 : 16 / 9} className="bg-sortmy-darker rounded-lg overflow-hidden">
        <iframe
          src={embedUrl}
          title={title || "YouTube video"}
          allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          className={`w-full h-full ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
          onLoad={handleLoad}
          onError={handleError}
        />
      </AspectRatio>
    </div>
  );
};

export default YoutubeShortEmbed;
