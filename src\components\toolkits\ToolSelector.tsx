import { Tool } from '@/types/tools';
import { Search } from 'lucide-react';
import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { Icons } from '@/components/icons';

interface ToolSelectorProps {
    availableTools: Tool[];
    selectedTools: string[];
    onChange: (selected: string[]) => void;
}

export function ToolSelector({ availableTools, selectedTools, onChange }: ToolSelectorProps) {
    const [searchQuery, setSearchQuery] = useState('');

    const filteredTools = availableTools.filter(tool =>
        tool.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    // Debug logging
    console.log('ToolSelector - Available tools:', availableTools.length);
    console.log('ToolSelector - Filtered tools:', filteredTools.length);
    console.log('ToolSelector - Sample tools:', availableTools.slice(0, 3).map(t => ({ id: t.id, name: t.name, logo_url: t.logo_url })));

    const toggleTool = (toolId: string) => {
        const newSelected = selectedTools.includes(toolId)
            ? selectedTools.filter(id => id !== toolId)
            : [...selectedTools, toolId];
        onChange(newSelected);
    };

    return (
        <div className="space-y-4">
            <div className="flex items-center gap-2">
                <Search className="w-4 h-4 text-gray-400" />
                <Input
                    placeholder="Search tools..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex-1"
                />
            </div>

            <ScrollArea className="h-[300px] rounded-md border p-4">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {filteredTools.map(tool => (
                        <button
                            key={tool.id}
                            onClick={() => toggleTool(tool.id)}
                            className={cn(
                                "flex flex-col items-center p-4 rounded-lg border transition-all",
                                selectedTools.includes(tool.id)
                                    ? "bg-sortmy-blue/20 border-sortmy-blue"
                                    : "border-transparent hover:border-sortmy-blue/50"
                            )}
                        >
                            {(tool.logo_url || tool.logoUrl) ? (
                                <img
                                    src={tool.logo_url || tool.logoUrl}
                                    alt={tool.name}
                                    className="w-12 h-12 rounded-lg object-contain"
                                    onError={(e) => {
                                        console.log('Image failed to load for tool:', tool.name, 'URL:', tool.logo_url || tool.logoUrl);
                                        e.currentTarget.style.display = 'none';
                                    }}
                                />
                            ) : (
                                <div className="w-12 h-12 rounded-lg bg-sortmy-blue/20 flex items-center justify-center">
                                    <Icons.Tool className="w-6 h-6 text-sortmy-blue/60" />
                                </div>
                            )}
                            <span className="mt-2 text-sm text-center font-medium">{tool.name}</span>
                        </button>
                    ))}
                </div>
            </ScrollArea>
        </div>
    );
}
