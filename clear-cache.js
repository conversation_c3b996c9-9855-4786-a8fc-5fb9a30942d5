#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('Clearing Vite cache and node_modules...');

// Remove node_modules/.vite directory if it exists
const viteCacheDir = path.join(__dirname, 'node_modules', '.vite');
if (fs.existsSync(viteCacheDir)) {
  fs.rmSync(viteCacheDir, { recursive: true, force: true });
  console.log('Removed .vite cache directory');
}

// Remove dist directory if it exists
const distDir = path.join(__dirname, 'dist');
if (fs.existsSync(distDir)) {
  fs.rmSync(distDir, { recursive: true, force: true });
  console.log('Removed dist directory');
}

console.log('Cache cleared! Please run npm install and then npm run dev');
