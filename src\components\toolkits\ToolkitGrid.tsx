import { Link } from 'react-router-dom';
import { Tool, Toolkit } from '@/types/tools';
import { ToolkitPreview } from './ToolkitPreview';

interface ToolkitGridProps {
    toolkits: Toolkit[];
    tools: Tool[];
    onEdit?: (toolkit: Toolkit) => void;
    onDelete?: (toolkit: Toolkit) => void;
}

export function ToolkitGrid({ toolkits, tools, onEdit, onDelete }: ToolkitGridProps) {
    if (!toolkits?.length) {
        return (
            <div className="text-center p-6 bg-sortmy-darker border border-sortmy-blue/20 rounded-lg">
                <p className="text-gray-400">No toolkits found</p>
            </div>
        );
    }

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {toolkits.map((toolkit) => (
                <Link
                    key={toolkit.id}
                    to={`/dashboard/toolkits/${toolkit.id}`}
                    className="block"
                >
                    <ToolkitPreview
                        toolkit={toolkit}
                        tools={tools}
                        onEdit={() => onEdit?.(toolkit)}
                        onDelete={() => onDelete?.(toolkit)}
                        isOwner={true}
                    />
                </Link>
            ))}
        </div>
    );
}
