import { useState, useCallback } from 'react';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Tool, Toolkit } from '@/types/tools';
import { useUsers } from './useUsers';
import { User } from '@/types';

interface UseToolkitReturn {
    toolkit: Toolkit | null;
    tools: Tool[];
    creator: User | null;
    loading: boolean;
    error: Error | null;
    fetchToolkit: (id: string) => Promise<void>;
}

export function useToolkit(): UseToolkitReturn {
    const [toolkit, setToolkit] = useState<Toolkit | null>(null);
    const [tools, setTools] = useState<Tool[]>([]);
    const [creator, setCreator] = useState<User | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);
    const { getUserById } = useUsers();

    const fetchToolkit = useCallback(async (id: string) => {
        setLoading(true);
        setError(null);
        try {
            const toolkitDoc = await getDoc(doc(db, 'toolkits', id));
            if (!toolkitDoc.exists()) {
                throw new Error('Toolkit not found');
            }

            const data = toolkitDoc.data() as Toolkit;
            const toolkitWithId = { ...data, id: toolkitDoc.id };
            setToolkit(toolkitWithId);            // Fetch tools data - check both collections
            const toolPromises = (data.tools || []).map(async (toolId: string) => {
                const toolDoc = await getDoc(doc(db, 'aiTools', toolId));
                if (toolDoc.exists()) {
                    return { id: toolDoc.id, ...toolDoc.data() } as Tool;
                }
                return null;
            });

            const resolvedTools = await Promise.all(toolPromises);
            const validTools = resolvedTools.filter((t): t is Tool => t !== null);
            setTools(validTools);

            // Fetch creator data if available
            if (data.created_by) {
                const creatorData = await getUserById(data.created_by);
                setCreator(creatorData);
            }
        } catch (err) {
            setError(err instanceof Error ? err : new Error('Failed to fetch toolkit'));
        } finally {
            setLoading(false);
        }
    }, [getUserById]);

    return {
        toolkit,
        tools,
        creator,
        loading,
        error,
        fetchToolkit
    };
}
