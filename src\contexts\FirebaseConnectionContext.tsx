import React, { createContext, useContext, useEffect, useState, useRef, useMemo } from 'react';
import { db } from '@/lib/firebase';
import { useToast } from '@/components/ui/use-toast';
import { enableNetwork, disableNetwork, doc, getDoc } from 'firebase/firestore';
import { networkManager } from '@/utils/networkUtils';

interface FirebaseConnectionState {
    isOnline: boolean;
    isInitializing: boolean;
    forceReconnect: () => Promise<void>;
    executeWithRetry: <T>(operation: () => Promise<T>, context: string) => Promise<T>;
}

export const FirebaseConnectionContext = createContext<FirebaseConnectionState | undefined>(undefined);

export function FirebaseConnectionProvider({ children }: { children: React.ReactNode }) {
    const [isOnline, setIsOnline] = useState(networkManager.getNetworkStatus());
    const [isInitializing, setIsInitializing] = useState(false);
    const { toast } = useToast();
    const prevOnlineState = useRef(networkManager.getNetworkStatus());
    const reconnectAttempts = useRef(0);
    const maxReconnectAttempts = 3;

    // Network status management with debounce
    useEffect(() => {
        let debounceTimer: NodeJS.Timeout;

        const handleNetworkChange = () => {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                const newStatus = networkManager.getNetworkStatus();
                setIsOnline(newStatus);
            }, 1000); // Debounce network status changes
        };

        window.addEventListener('online', handleNetworkChange);
        window.addEventListener('offline', handleNetworkChange);

        return () => {
            window.removeEventListener('online', handleNetworkChange);
            window.removeEventListener('offline', handleNetworkChange);
            clearTimeout(debounceTimer);
        };
    }, []);

    // Handle network state changes and Firestore connectivity
    useEffect(() => {
        const isPreview = window.location.href.includes('cluster-iktsryn7xnhpexlu6255bftka4');
        if (isPreview) return;

        if (prevOnlineState.current !== isOnline) {
            if (isOnline) {
                networkManager.executeWithRetry(
                    async () => {
                        await enableNetwork(db);
                        reconnectAttempts.current = 0; // Reset reconnect attempts on successful connection
                        toast({
                            title: 'Connected',
                            description: 'You are now connected to the server.',
                        });
                    },
                    'enable network'
                ).catch(console.error);
            } else {
                networkManager.executeWithRetry(
                    async () => {
                        await disableNetwork(db);
                        toast({
                            title: 'Disconnected',
                            description: 'You are currently offline. Some features may be limited.',
                            variant: 'destructive',
                        });
                    },
                    'disable network'
                ).catch(console.error);
            }
            prevOnlineState.current = isOnline;
        }
    }, [isOnline, toast]);

    const contextValue = useMemo<FirebaseConnectionState>(() => ({
        isOnline,
        isInitializing,
        forceReconnect: async () => {
            try {
                if (reconnectAttempts.current >= maxReconnectAttempts) {
                    toast({
                        title: "Connection Error",
                        description: "Maximum reconnection attempts reached. Please try again later.",
                        variant: "destructive"
                    });
                    return;
                }

                setIsInitializing(true);
                reconnectAttempts.current++;

                await networkManager.executeWithRetry(
                    async () => {
                        // Disconnect first
                        await disableNetwork(db);
                        // Wait a moment before reconnecting
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        // Reconnect
                        await enableNetwork(db);
                        // Test the connection with exponential backoff
                        const testRef = doc(db, '_connection_test', 'status');
                        await getDoc(testRef);
                        // Update status
                        setIsOnline(true);
                        reconnectAttempts.current = 0; // Reset on successful reconnection
                        toast({
                            title: "Connection Restored",
                            description: "Successfully reconnected to the server",
                        });
                    },
                    'Force reconnect'
                );
            } catch (error) {
                setIsOnline(false);
                toast({
                    title: "Connection Error",
                    description: `Failed to reconnect to the server. Attempt ${reconnectAttempts.current}/${maxReconnectAttempts}`,
                    variant: "destructive"
                });
            } finally {
                setIsInitializing(false);
            }
        },
        executeWithRetry: networkManager.executeWithRetry
    }), [isOnline, isInitializing, toast]);

    return (
        <FirebaseConnectionContext.Provider value={contextValue}>
            {children}
        </FirebaseConnectionContext.Provider>
    );
}

export function useFirebaseConnection() {
    const context = useContext(FirebaseConnectionContext);
    if (context === undefined) {
        throw new Error('useFirebaseConnection must be used within a FirebaseConnectionProvider');
    }
    return context;
}
