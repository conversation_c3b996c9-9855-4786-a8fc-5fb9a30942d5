import { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { PlusCircle, Loader2, Briefcase } from 'lucide-react';
import { collection, doc, getDoc, getDocs, query, where, updateDoc, arrayUnion, arrayRemove, deleteDoc, setDoc } from 'firebase/firestore';
import { db, storage } from '@/lib/firebase';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import type { Tool, UserTool, ImagePositionType, Toolkit } from '@/types/tools';
import type { AITool } from '@/types';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ImageCropper } from '@/components/image/ImageCropper';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogHeader,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { ToolSearch } from './ToolSearch';
import { TagsFilter } from './TagsFilter';
import { ToolGrid } from './ToolGrid';
import { ClickEffect } from '@/components/ui/click-effect';
import { NeonButton } from '@/components/ui/neon-button';
import { ToolkitManager } from '@/components/toolkits/ToolkitManager';
import { ToolkitPreview } from '@/components/toolkits/ToolkitPreview';

// Helper functions for tag and image handling
// Update the type checking for string arrays
const ensureStringArray = (tags: string | string[] | undefined | null): string[] => {
  if (!tags) return [];
  if (Array.isArray(tags)) return tags.filter(Boolean);
  if (typeof tags === 'string') {
    return tags.split(',').map((tag: string) => tag.trim()).filter(Boolean);
  }
  return [];
};

const fixImageProperties = (tool: Tool): Tool => {
  return {
    ...tool,
    image_settings: tool.image_settings || {
      size: 'contain' as const,
      position: 'center' as ImagePositionType,
      padding: 0,
      scale: 1,
      rotate: 0
    }
  };
};

// Move EmptyUserTools component definition to the top of the file, after imports
interface EmptyUserToolsProps {
  onClick?: () => void;
}

const EmptyUserTools: React.FC<EmptyUserToolsProps> = () => (
  <div className="bg-sortmy-darker border border-[#01AAE9]/20 rounded-lg p-6 text-center">
    <Briefcase className="mx-auto w-12 h-12 mb-3 opacity-30" />
    <p className="text-gray-400">You haven't added any tools yet</p>
    <div className="flex justify-center mt-4">
      <Button
        variant="default"
        onClick={() => {
          const librarySection = document.getElementById('library-section');
          librarySection?.scrollIntoView({ behavior: 'smooth' });
        }}
        className="flex items-center gap-2"
      >
        <PlusCircle className="w-4 h-4" />
        Browse Library
      </Button>
    </div>
  </div>
);

// CombinedToolTracker Component
export const CombinedToolTracker = () => {
  const { user, isAdmin } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const libraryRef = useRef<HTMLDivElement>(null);

  // Common state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [allTags, setAllTags] = useState<string[]>([]);

  // Search suggestions state
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Define types for search suggestions
  type SuggestionType = 'name' | 'description' | 'category' | 'tag';

  interface SearchSuggestion {
    text: string;
    type: SuggestionType;
    source: 'user' | 'library' | 'both';
    toolId?: string;
  }

  // User tools state
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [toolToDelete, setToolToDelete] = useState<Tool | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  // Library tools state
  const [savingToolIds, setSavingToolIds] = useState<string[]>([]);
  const [editingTool, setEditingTool] = useState<Tool | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isEditingImage, setIsEditingImage] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  // Toolkits state
  const [showToolkitManager, setShowToolkitManager] = useState(false);
  const [editingToolkit, setEditingToolkit] = useState<Toolkit | undefined>(undefined);

  // Update the toolkits query with proper loading state
  const { data: userToolkits = [], isLoading: isLoadingToolkits } = useQuery<Toolkit[]>({
    queryKey: ['toolkits', user?.uid],
    queryFn: async () => {
      if (!user?.uid) return [];
      const toolkitsRef = collection(db, 'toolkits');
      const q = query(toolkitsRef, where('created_by', '==', user.uid));
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Toolkit[];
    },
    enabled: !!user?.uid,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  });

  // Update the userTools query with error handling
  const { data: userTools, isLoading: isLoadingUserTools, error: userToolsError } = useQuery<UserTool[]>({
    queryKey: ['userTools', user?.uid],
    queryFn: async () => {
      if (!user?.uid) return [];

      try {
        // Fetch from tools collection
        const toolsRef = collection(db, 'tools');
        const q = query(toolsRef, where('user_id', '==', user?.uid));
        const snapshot = await getDocs(q); const toolsFromCollection = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          source: 'tools_collection' as const
        })) as UserTool[];

        // Fetch from user's toolTracker array
        const userRef = doc(db, 'users', user.uid);
        const userDoc = await getDoc(userRef);

        if (!userDoc.exists()) return toolsFromCollection;

        const userData = userDoc.data();
        const toolTracker = userData.toolTracker || [];

        // Convert toolTracker items to Tool format
        const toolsFromTracker = toolTracker.map((tool: any) => {
          // Extract tags from the tool if available
          let tags: string[] = [];
          if (tool.tags) {
            tags = Array.isArray(tool.tags) ? tool.tags :
              typeof tool.tags === 'string' ? tool.tags.split(',').map((t: string) => t.trim()) : [];
          }

          return {
            id: tool.id,
            name: tool.name,
            description: tool.useCase || tool.description || '',
            logo_url: tool.logoUrl || tool.logoLink,
            website_url: tool.website || tool.websiteLink,
            website: tool.website || tool.websiteLink,
            tags: tags,
            created_at: tool.addedAt || new Date().toISOString(),
            user_id: user.uid,
            category: tool.category || '',
            price_tier: tool.pricing || 'free',
            notes: tool.excelsAt || '',
            source: 'user_tooltracker'
          };
        });

        return [...toolsFromCollection, ...toolsFromTracker] as (Tool & { source: string })[];
      } catch (error) {
        console.error('Error fetching user tools:', error);
        throw error;
      }
    },
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    enabled: !!user?.uid,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes (formerly cacheTime)
  });

  // Update the library tools query with error handling
  const { data: libraryTools, isLoading: isLoadingLibrary } = useQuery<Tool[]>({
    queryKey: ['aiTools'],
    queryFn: async () => {
      try {
        const toolsCollection = collection(db, 'aiTools');
        const toolsSnapshot = await getDocs(toolsCollection);

        const toolsList: Tool[] = [];
        toolsSnapshot.forEach((doc) => {
          const aiToolData = doc.data() as AITool; // Use AITool type
          // Transform AITool to Tool interface
          const tool: Tool = {
            id: doc.id,
            name: aiToolData.name,
            description: aiToolData.description,
            logo_url: aiToolData.logoUrl, // Map logoUrl to logo_url
            logoUrl: aiToolData.logoUrl, // Keep both for compatibility
            website: aiToolData.website,
            tags: ensureStringArray(aiToolData.tags),
            category: aiToolData.category,
            created_at: aiToolData.createdAt || new Date().toISOString(),
            updated_at: aiToolData.updatedAt,
            user_id: aiToolData.createdBy,
            pricing: aiToolData.pricing,
            useCase: aiToolData.useCase,
            excelsAt: aiToolData.excelsAt,
            source: 'library',
            image_settings: aiToolData.imageSettings
          };

          toolsList.push(tool);
        });

        return toolsList;
      } catch (error) {
        console.error('Error fetching library tools:', error);
        throw error;
      }
    },
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  });

  // Loading and error states
  const isLoading = isLoadingUserTools || isLoadingLibrary || isLoadingToolkits;
  const error = userToolsError;

  // Extract tags from a tool
  const extractTags = (tool: Tool): string[] => {
    if (!tool.tags) return [];
    if (Array.isArray(tool.tags)) return tool.tags;
    return ensureStringArray(tool.tags);
  };

  // Add tags from a tool to a Set
  const addToolTagsToSet = (tagsSet: Set<string>, tool: Tool) => {
    const tags = extractTags(tool);
    tags.forEach(tag => tag && tagsSet.add(tag));
  };

  // Update all unique tags from both user tools and library tools
  useEffect(() => {
    const tagsSet = new Set<string>();

    if (userTools?.length) {
      userTools.forEach(tool => addToolTagsToSet(tagsSet, tool));
    }

    if (libraryTools?.length) {
      libraryTools.forEach(tool => addToolTagsToSet(tagsSet, tool));
    }

    setAllTags(Array.from(tagsSet));
  }, [userTools, libraryTools]);

  // Generate search suggestions based on the search query
  const generateSuggestions = (query: string) => {
    if (!query || query.length < 2) {
      setSuggestions([]);
      return;
    }

    const queryLower = query.toLowerCase();
    const newSuggestions: SearchSuggestion[] = [];
    const addedSuggestions = new Set<string>(); // To avoid duplicates

    // Helper function to add a suggestion if it's not already added
    const addSuggestion = (text: string, type: SuggestionType, source: 'user' | 'library' | 'both', toolId?: string) => {
      const key = `${text}-${type}`;
      if (!addedSuggestions.has(key) && text.toLowerCase().includes(queryLower)) {
        newSuggestions.push({ text, type, source, toolId });
        addedSuggestions.add(key);
      }
    };

    // Add suggestions from user tools
    if (userTools?.length) {
      userTools.forEach(tool => {
        // Process tool properties and tags
        const toolTags = extractTags(tool);
        toolTags.forEach(tag => {
          tag && addSuggestion(tag, 'tag', 'user', tool.id);
        });

        // Add name suggestions
        if ((tool as any).name) {
          addSuggestion((tool as any).name, 'name', 'user', tool.id);
        }

        // Add description suggestions
        if ((tool as any).description) {
          const desc = (tool as any).description;
          // Only add if the description is not too long and contains the query
          if (desc.length < 50) {
            addSuggestion(desc, 'description', 'user', tool.id);
          }
        }

        // Add category suggestions
        if ((tool as any).category) {
          addSuggestion((tool as any).category, 'category', 'user', tool.id);
        }
      });
    }

    // Add suggestions from library tools
    if (libraryTools && libraryTools.length > 0) {
      libraryTools.forEach(tool => {
        // Add name suggestions
        addSuggestion(tool.name, 'name', 'library', tool.id);

        // Add description/useCase suggestions
        if (tool.description && tool.description.length < 50) {
          addSuggestion(tool.description, 'description', 'library', tool.id);
        }
        if (tool.useCase && tool.useCase.length < 50) {
          addSuggestion(tool.useCase, 'description', 'library', tool.id);
        }

        // Add tag suggestions
        if (tool.tags) {
          const tags = ensureStringArray(tool.tags);

          tags.forEach((tag: string) => {
            // Check if this tag also exists in user tools
            const existsInUserTools = userTools?.some(userTool =>
              userTool.tags &&
              Array.isArray(userTool.tags) &&
              userTool.tags.includes(tag)
            );

            addSuggestion(tag, 'tag', existsInUserTools ? 'both' : 'library', tool.id);
          });
        }
      });
    }

    // Limit the number of suggestions to avoid overwhelming the UI
    setSuggestions(newSuggestions.slice(0, 10));
  };

  // Update suggestions when search query changes
  useEffect(() => {
    generateSuggestions(searchQuery);
  }, [searchQuery, userTools, libraryTools]);

  // Handle clicks outside the suggestions dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        showSuggestions &&
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target as Node) &&
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSuggestions]);

  // Filter user tools
  const filteredUserTools = userTools && Array.isArray(userTools) ? userTools.filter((tool: UserTool) => {
    // Filter by search query
    const searchLower = searchQuery.toLowerCase();
    const matchesSearch = (
      (tool as any).name?.toLowerCase().includes(searchLower) ||
      (tool as any).description?.toLowerCase().includes(searchLower) ||
      ((tool as any).tags && Array.isArray((tool as any).tags) && (tool as any).tags.some((tag: string) =>
        typeof tag === 'string' && tag.toLowerCase().includes(searchLower)
      ))
    );

    // Filter by selected tags
    const matchesTags = selectedTags.length === 0 || (
      (tool as any).tags &&
      Array.isArray((tool as any).tags) &&
      selectedTags.every(tag => (tool as any).tags.includes(tag))
    );

    return matchesSearch && matchesTags;
  }) : [];

  // Filter library tools
  const filteredLibraryTools = libraryTools && Array.isArray(libraryTools) ? libraryTools.filter((tool: Tool) => {
    const searchLower = searchQuery.toLowerCase();
    const matchesSearch = (
      tool.name.toLowerCase().includes(searchLower) ||
      (tool.description && tool.description.toLowerCase().includes(searchLower)) ||
      (tool.useCase && tool.useCase.toLowerCase().includes(searchLower)) ||
      (tool.tags && Array.isArray(tool.tags) && tool.tags.some((tag: string) =>
        typeof tag === 'string' && tag.toLowerCase().includes(searchLower)
      ))
    );

    // Fix type assertion for tags
    const matchesTags = selectedTags.length === 0 || (
      tool.tags &&
      Array.isArray(tool.tags) &&
      selectedTags.every((tag: string) => tool.tags.includes(tag))
    );

    return matchesSearch && matchesTags;
  }) : [];

  // Toggle tag selection
  const toggleTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  // Handle selecting a suggestion
  const handleSelectSuggestion = (suggestion: SearchSuggestion) => {
    // If it's a tag, add it to selected tags
    if (suggestion.type === 'tag') {
      if (!selectedTags.includes(suggestion.text)) {
        toggleTag(suggestion.text);
      }
      // Clear the search query if it was used to find this tag
      if (searchQuery.toLowerCase().includes(suggestion.text.toLowerCase())) {
        setSearchQuery('');
      }
    } else {
      // For other types, set the search query to the suggestion text
      setSearchQuery(suggestion.text);
    }

    // Hide suggestions after selection
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);

    // Focus back on the input
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Handle keyboard navigation for suggestions
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev =>
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : 0);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0 && selectedSuggestionIndex < suggestions.length) {
          handleSelectSuggestion(suggestions[selectedSuggestionIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
        break;
      default:
        break;
    }
  };
  // Handle deleting a user tool
  const handleDeleteClick = (tool: any) => {
    // Check if trying to delete a library tool without admin privileges
    if ((tool as any).source === 'library' && !isAdmin) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can delete library tools.',
        variant: 'destructive',
      });
      return;
    }
    setToolToDelete(tool as Tool);
    setShowDeleteDialog(true);
  };

  // Confirm deletion of a user tool
  const confirmDelete = async () => {
    if (!toolToDelete) return;

    setIsDeleting(true);
    try {
      // Check the source of the tool to determine how to delete it
      if ((toolToDelete as any).source === 'tools_collection') {
        // Delete from tools collection
        await deleteDoc(doc(db, 'tools', toolToDelete.id));
      } else if ((toolToDelete as any).source === 'user_tooltracker') {
        // Delete from user's toolTracker array
        const userRef = doc(db, 'users', user!.uid);

        // Get the current toolTracker array
        const userDoc = await getDoc(userRef);
        if (userDoc.exists()) {
          const userData = userDoc.data();
          const toolTracker = userData.toolTracker || [];

          // Find the tool to remove
          const toolToRemove = toolTracker.find((tool: any) => tool.id === toolToDelete.id);

          if (toolToRemove) {
            // Remove the tool from the array
            await updateDoc(userRef, {
              toolTracker: arrayRemove(toolToRemove)
            });
          }
        }
      }

      // Invalidate and refetch the userTools query
      await queryClient.invalidateQueries({
        queryKey: ['userTools'] as const,
        exact: true,
      });

      toast({
        title: "Tool deleted",
        description: "Your tool has been removed successfully.",
      });
    } catch (error) {
      console.error('Error deleting tool:', error);
      toast({
        title: "Error",
        description: "Failed to delete the tool. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
      setToolToDelete(null);
    }
  };

  // Add a library tool to user's tools
  const addToolToTracker = async (tool: Tool) => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please sign in to add tools to your tracker.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSavingToolIds(prev => [...prev, tool.id]);
      const processedTool = fixImageProperties(tool);
      const toolData = {
        ...processedTool,
        tags: ensureStringArray(tool.tags),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Add to Firestore
      await updateDoc(doc(db, 'users', user.uid), {
        toolTracker: arrayUnion(toolData)
      });

      // Update local state with proper type assertion
      await queryClient.invalidateQueries({
        queryKey: ['userTools'],
        exact: true,
        refetchType: 'all',
        type: 'all'
      } as const);

      toast({
        title: 'Tool Added',
        description: 'Tool has been added to your tracker.',
      });
    } catch (error) {
      console.error('Error adding tool:', error);
      toast({
        title: 'Error',
        description: 'Failed to add tool to tracker.',
        variant: 'destructive',
      });
    } finally {
      setSavingToolIds(prev => prev.filter(id => id !== tool.id));
    }
  };

  // Handle editing a tool (admin only)
  const handleEditTool = async (tool: Tool) => {
    if (!isAdmin) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can edit tools.',
        variant: 'destructive',
      });
      return;
    }
    setEditingTool(tool);
    setIsEditing(true);
  };

  // Handle saving edited tool
  const handleSaveEdit = async (updatedTool: Tool) => {
    try {
      const toolRef = doc(db, 'tools', updatedTool.id);
      const toolData = {
        ...updatedTool,
        updated_at: new Date().toISOString(),
        tags: ensureStringArray(updatedTool.tags),
        image_settings: updatedTool.image_settings,
      };

      await updateDoc(toolRef, toolData);

      // Update local state
      queryClient.setQueriesData({ queryKey: ['userTools', user?.uid] }, (old: any) => {
        if (!Array.isArray(old)) return old;
        return old.map((t: Tool) =>
          t.id === updatedTool.id ? { ...t, ...toolData } : t
        );
      });

      setIsEditing(false);
      setEditingTool(null);
      toast({
        title: 'Tool updated',
        description: 'The tool has been updated successfully.',
      });
    } catch (error) {
      console.error('Error updating tool:', error);
      toast({
        title: 'Error',
        description: 'Failed to update the tool.',
        variant: 'destructive',
      });
    }
  };

  const handleImageUpload = async (file: File, toolId: string) => {
    try {
      setIsUploading(true);
      const logoRef = ref(storage, `tool-logos/${toolId}/${file.name}`);
      await uploadBytes(logoRef, file);
      const logo_url = await getDownloadURL(logoRef);

      if (editingTool) {
        setEditingTool(prev => ({
          ...prev!,
          logo_url,
        }));
      }

      return logo_url;
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: 'Error',
        description: 'Failed to upload image.',
        variant: 'destructive',
      });
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  // Type for object-fit in image style
  const getObjectFit = (size?: string): 'contain' | 'cover' => {
    if (size === 'contain' || size === 'cover') return size;
    return 'contain';
  };

  // Update handleToolkitSave to fix type error with arrayUnion
  const handleToolkitSave = async (toolkit: Toolkit) => {
    try {
      if (!user?.uid) return;

      const toolkitRef = toolkit.id ?
        doc(db, 'toolkits', toolkit.id) :
        doc(collection(db, 'toolkits'));

      const toolkitData = {
        ...toolkit,
        created_by: user.uid,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      if (toolkit.id) {
        await updateDoc(toolkitRef, toolkitData);
      } else {
        await setDoc(toolkitRef, {
          ...toolkitData,
          id: toolkitRef.id,
        });
      }

      // Invalidate and refetch toolkits
      await queryClient.invalidateQueries({ queryKey: ['toolkits', user.uid] });

      setShowToolkitManager(false);
      setEditingToolkit(undefined);

      toast({
        title: toolkit.id ? 'Toolkit Updated' : 'Toolkit Created',
        description: `Your toolkit has been ${toolkit.id ? 'updated' : 'created'} successfully.`
      });
    } catch (error) {
      console.error('Error saving toolkit:', error);
      toast({
        title: 'Error',
        description: 'Failed to save toolkit. Please try again.',
        variant: 'destructive'
      });
    }
  };

  // Handle deleting a toolkit
  const handleToolkitDelete = async (toolkit: Toolkit) => {
    if (!user?.uid) return;
    try {
      await deleteDoc(doc(db, 'toolkits', toolkit.id));
      toast({
        title: 'Success',
        description: 'Toolkit deleted successfully'
      });
      // Refresh toolkits
      queryClient.invalidateQueries({ queryKey: ['toolkits', user.uid] });
    } catch (error) {
      console.error('Error deleting toolkit:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete toolkit',
        variant: 'destructive'
      });
    }
  };

  // Add handleEditToolkit function before render
  const handleEditToolkit = (toolkit: Toolkit) => {
    setEditingToolkit(toolkit);
    setShowToolkitManager(true);
  };

  // Render
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Tool Tracker</h2>
        </div>        <div className="flex gap-3">          {isAdmin && (
          <ClickEffect effect="ripple" color="blue">
            <Link to="/dashboard/tools/library/add">
              <NeonButton variant="magenta">
                <PlusCircle className="w-4 h-4 mr-2" />
                Add to Library
              </NeonButton>
            </Link>
          </ClickEffect>
        )}          <ClickEffect effect="ripple" color="blue">
            <Link to="/dashboard/tools/add">
              <NeonButton variant="blue">
                <PlusCircle className="w-4 h-4 mr-2" />
                Add Tool
              </NeonButton>
            </Link>
          </ClickEffect>
        </div>
      </div>

      {/* Search */}
      <ToolSearch
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        suggestions={suggestions}
        showSuggestions={showSuggestions}
        setShowSuggestions={setShowSuggestions}
        handleSelectSuggestion={handleSelectSuggestion}
        selectedSuggestionIndex={selectedSuggestionIndex}
        handleKeyDown={handleKeyDown}
      />

      {/* Tags Filter */}
      <TagsFilter
        allTags={allTags}
        selectedTags={selectedTags}
        toggleTag={toggleTag}
        clearTags={() => setSelectedTags([])}
      />

      {/* User's Tools Section */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold text-white">Your Tools</h3>
        {filteredUserTools.length === 0 ? (
          <EmptyUserTools />
        ) : (
          <ToolGrid
            tools={filteredUserTools}
            isLoading={isLoading}
            error={error}
            onDelete={handleDeleteClick}
            emptyMessage="You haven't added any tools yet"
          />
        )}
      </div>

      {/* Separator */}
      <Separator className="bg-sortmy-blue/20 my-8" />

      {/* Toolkits Section */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-xl font-semibold text-white">Toolkits</h3>
          <NeonButton
            variant="blue"
            onClick={() => setShowToolkitManager(true)}
            disabled={isLoadingToolkits}
          >
            {isLoadingToolkits ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                <PlusCircle className="w-4 h-4 mr-2" />
                Create Toolkit
              </>
            )}
          </NeonButton>
        </div>

        {isLoadingToolkits ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <div
                key={`toolkit-skeleton-${i}`}
                className="h-48 bg-sortmy-darker/40 border border-sortmy-blue/20 rounded-lg animate-pulse"
              />
            ))}
          </div>
        ) : (
          <ToolkitGrid
            toolkits={userToolkits}
            tools={[...filteredUserTools, ...filteredLibraryTools]}
            onEdit={handleEditToolkit}
            onDelete={handleToolkitDelete}
          />
        )}
      </div>

      {/* Separator */}
      <Separator className="bg-sortmy-blue/20 my-8" />

      {/* AI Tools Library Section */}
      <div ref={libraryRef} id="library-section" className="space-y-4">
        <h3 className="text-xl font-semibold text-white">AI Tools Library</h3>
        <p className="text-gray-400">Discover and add new AI tools to your collection</p>
        <ToolGrid
          tools={filteredLibraryTools}
          isLoading={isLoadingLibrary}
          onAdd={addToolToTracker}
          onEdit={handleEditTool}
          onDelete={isAdmin ? handleDeleteClick : undefined}
          isAdmin={isAdmin}
          savingToolIds={savingToolIds}
          emptyMessage="No AI tools found matching your search criteria."
        />
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent className="bg-sortmy-dark border-[#01AAE9]/20 backdrop-blur-md">
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Tool?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{(toolToDelete as any)?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <button
              className="px-4 py-2 rounded-md bg-transparent border border-[#01AAE9]/20 text-white hover:bg-[#01AAE9]/10 transition-colors"
              disabled={isDeleting}
              onClick={() => setShowDeleteDialog(false)}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 rounded-md bg-red-500/80 text-white hover:bg-red-500 transition-colors"
              onClick={confirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 inline animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit Tool Dialog */}
      <Dialog open={isEditing} onOpenChange={(open) => !open && setIsEditing(false)}>
        <DialogContent className="bg-sortmy-dark border-[#01AAE9]/20 backdrop-blur-md max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Tool</DialogTitle>
            <DialogDescription>
              Update the tool details below.
            </DialogDescription>
          </DialogHeader>

          {editingTool && (
            <div className="space-y-6 py-4">
              {/* Name and Description */}
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={editingTool.name}
                    onChange={(e) => {
                      setEditingTool({ ...editingTool, name: e.target.value });
                    }}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={editingTool.description || ''}
                    onChange={(e) => {
                      setEditingTool({ ...editingTool, description: e.target.value });
                    }}
                    className="min-h-[100px]"
                  />
                </div>
              </div>

              {/* Image Section */}
              <div className="space-y-4">
                <Label>Tool Logo</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Image Preview */}
                  <div className="space-y-4">
                    <div className="relative w-40 h-40 mx-auto rounded-2xl overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg">
                      {editingTool.logoUrl ? (
                        <img
                          src={editingTool.logo_url}
                          alt={`${editingTool.name} logo`}
                          className="w-full h-full"
                          crossOrigin="anonymous"
                          style={{
                            objectFit: getObjectFit(editingTool.image_settings?.size),
                            padding: `${editingTool.image_settings?.padding || 0}px`,
                            objectPosition: editingTool.image_settings?.position || 'center',
                            transform: `scale(${editingTool.image_settings?.scale || 1}) rotate(${editingTool.image_settings?.rotate || 0}deg)`
                          }}
                          onError={(e) => {
                            const imgElement = e.currentTarget;
                            if (imgElement.src.includes('?')) {
                              // Try without parameters
                              imgElement.src = imgElement.src.split('?')[0];
                            } else {
                              // Show fallback
                              imgElement.style.display = 'none';
                              const parent = imgElement.parentElement;
                              if (parent) {
                                parent.classList.add('flex', 'items-center', 'justify-center');
                                const fallback = document.createElement('span');
                                fallback.className = 'text-4xl font-bold text-white';
                                fallback.textContent = editingTool.name.charAt(0);
                                parent.appendChild(fallback);
                              }
                            }
                          }}
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <span className="text-4xl font-bold text-white">{editingTool.name.charAt(0)}</span>
                        </div>
                      )}
                    </div>

                    {/* Upload Controls */}
                    <div className="flex flex-col gap-2">
                      <Label
                        htmlFor="logo-upload"
                        className="flex flex-col items-center justify-center p-4 border-2 border-dashed border-[#01AAE9]/40 rounded-xl cursor-pointer hover:border-[#01AAE9]/60 transition-colors"
                      >
                        <input
                          id="logo-upload"
                          type="file"
                          className="hidden"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file && editingTool) {
                              handleImageUpload(file, editingTool.id);
                            }
                          }}
                          disabled={isUploading}
                        />
                        <div className="flex flex-col items-center gap-2 text-gray-400">
                          {isUploading ? (
                            <>
                              <Loader2 className="w-6 h-6 animate-spin" />
                              <span>Uploading...</span>
                            </>
                          ) : (
                            <>
                              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              <span>Click to upload image</span>
                              <span className="text-xs text-gray-500">SVG, PNG, JPG or GIF (max. 5MB)</span>
                            </>
                          )}
                        </div>
                      </Label>

                      <div className="relative">
                        <Input
                          value={editingTool.logoUrl || ''}
                          onChange={(e) => {
                            setEditingTool({ ...editingTool, logoUrl: e.target.value });
                          }}
                          placeholder="Or enter image URL..."
                          className="pr-20"
                        />
                        {editingTool.logoUrl && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-1 top-1 h-7"
                            onClick={() => setEditingTool({ ...editingTool, logoUrl: '' })}
                          >
                            Clear
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Image Settings */}
                  <div className="space-y-4">
                    <Label>Image Settings</Label>
                    {editingTool.logoUrl ? (
                      isEditingImage ? (
                        <ImageCropper
                          imageUrl={editingTool.logo_url}
                          settings={editingTool.image_settings}
                          onComplete={(originalUrl, settings) => {
                            setEditingTool(prev => ({
                              ...prev!,
                              logo_url: originalUrl,
                              image_settings: settings
                            }));
                            setIsEditingImage(false);
                          }}
                        />
                      ) : (
                        <Button
                          type="button"
                          variant="outline"
                          className="w-full"
                          onClick={() => setIsEditingImage(true)}
                        >
                          Adjust Image Settings
                        </Button>
                      )
                    ) : (
                      <p className="text-sm text-gray-400 text-center p-4">
                        Upload or provide an image URL to adjust settings
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Label htmlFor="tags">Tags</Label>
                <Input
                  id="tags"
                  value={Array.isArray(editingTool.tags) ? editingTool.tags.join(', ') : editingTool.tags || ''}
                  onChange={(e) => {
                    const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
                    setEditingTool({ ...editingTool, tags });
                  }}
                  placeholder="Enter tags separated by commas"
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditing(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => editingTool && handleSaveEdit(editingTool)}
              disabled={!editingTool || isUploading}
            >
              {isUploading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Toolkit Manager Dialog */}
      <ToolkitManager
        isOpen={showToolkitManager}
        onClose={() => {
          setShowToolkitManager(false);
          setEditingToolkit(undefined);
        }}
        availableTools={[...filteredUserTools, ...filteredLibraryTools]}
        onSave={handleToolkitSave}
        editingToolkit={editingToolkit}
      />
    </div>
  );
};

export default CombinedToolTracker;

// Toolkit Grid Component
interface ToolkitGridProps {
  toolkits: Toolkit[];
  tools: Tool[]; // Add this
  onEdit?: (toolkit: Toolkit) => void;
  onDelete?: (toolkit: Toolkit) => void;
}

function ToolkitGrid({ toolkits, tools, onEdit, onDelete }: ToolkitGridProps) {
  if (!toolkits?.length) {
    return (
      <div className="text-center p-6 bg-sortmy-darker border border-sortmy-blue/20 rounded-lg">
        <p className="text-gray-400">No toolkits found</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
      {toolkits.map((toolkit) => (
        <Link
          key={`toolkit-${toolkit.id}-${toolkit.created_at}`}
          to={`/dashboard/toolkits/${toolkit.id}`}
          className="block"
        >
          <ToolkitPreview
            toolkit={toolkit}
            tools={tools}
            onEdit={(e) => onEdit?.(toolkit)}
            onDelete={(e) => onDelete?.(toolkit)}
            isOwner={true}
          />
        </Link>
      ))}
    </div>
  );
}