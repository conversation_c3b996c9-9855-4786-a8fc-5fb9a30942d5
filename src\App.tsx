import { Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ProtectedRoute from './components/ProtectedRoute';
import DashboardLayout from './components/DashboardLayout';
import PushNotificationInitializer from './components/PushNotificationInitializer';
import PortfolioProvider from './contexts/PortfolioContext';
import BackgroundProvider from './contexts/BackgroundContext';
import { useAuth } from './contexts/AuthContext';

// Pages
import SimpleLogin from '@/pages/SimpleLogin';
import EmailLogin from '@/pages/EmailLogin';
import Signup from '@/pages/Signup';
import Profile from '@/pages/Profile';
import Settings from '@/pages/Settings';
import AddTool from '@/components/dashboard/AddTool';
import CombinedToolTracker from '@/components/dashboard/CombinedToolTracker';
import Portfolio from '@/components/dashboard/Portfolio';
import AddPortfolio from '@/components/dashboard/AddPortfolio';
import ExploreCreators from '@/components/dashboard/ExploreCreators';
import ExplorePosts from '@/components/dashboard/ExplorePosts';
import AdminAddLibraryTool from '@/components/dashboard/AdminAddLibraryTool';
import InstagramStylePortfolio from '@/pages/InstagramStylePortfolio';
import Index from '@/pages/Index';
import Dashboard from '@/components/Dashboard';
import Explore from '@/components/dashboard/Explore';
import Achievements from '@/pages/Achievements';
import { useEffect } from 'react';
import Academy from '@/pages/Academy';
import AIToolsUpload from '@/pages/AIToolsUpload';

import Messages from '@/pages/Messages';
import UserInteractions from '@/pages/UserInteractions';
import Analytics from '@/pages/Analytics';
import { initializeCapacitor } from '@/lib/capacitor';
import '@/lib/debug-utils'; // Import debug utilities
import ToolkitDetail from '@/components/toolkits/ToolkitDetail';
import ToolDetail from '@/components/tools/ToolDetail';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: false,
    },
  },
});



function App() {
  const { user, isLoading } = useAuth();

  // Initialize Capacitor when the app starts
  useEffect(() => {
    initializeCapacitor().catch(console.error);
  }, []);

  // Wait for auth to load
  if (isLoading) {
    return null; // or your loading spinner
  } return (
    <QueryClientProvider client={queryClient}>
      <BackgroundProvider>
        <PortfolioProvider>
          <PushNotificationInitializer />
          <Routes>
            {/* Redirect root to dashboard if logged in */}
            <Route path="/" element={user ? <Navigate to="/dashboard" replace /> : <Index />} />
            {/* Redirect login to dashboard if already logged in */}
            <Route path="/login" element={user ? <Navigate to="/dashboard" replace /> : <EmailLogin />} />
            <Route path="/popup-login" element={user ? <Navigate to="/dashboard" replace /> : <SimpleLogin />} />
            {/* Redirect old login to new login page */}
            <Route path="/old-login" element={<Navigate to="/login" replace />} />
            <Route path="/signup" element={user ? <Navigate to="/dashboard" replace /> : <Signup />} />

            <Route path="/portfolio/:username" element={
              <div className="public-profile-container">
                <InstagramStylePortfolio />
              </div>
            } />
            <Route path="/explore" element={<ExploreCreators />} />
            <Route path="/explore/posts" element={<ExplorePosts />} />

            {/* Dashboard and related routes */}
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Dashboard />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            <Route path="/dashboard/profile" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Profile />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            <Route path="/dashboard/settings" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Settings />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            <Route path="/dashboard/tools" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <CombinedToolTracker />
                </DashboardLayout>
              </ProtectedRoute>
            } />            <Route path="/dashboard/tools/add" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <AddTool />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            <Route path="/dashboard/tools/library/add" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <AdminAddLibraryTool />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            {/* Redirect old library path to the combined tool tracker */}
            <Route path="/dashboard/tools/library" element={
              <Navigate to="/dashboard/tools" replace />
            } />

            <Route path="/dashboard/portfolio" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Portfolio />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            {/* This route is redundant - using InstagramStylePortfolio instead */}

            <Route path="/dashboard/portfolio/add" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <AddPortfolio />
                </DashboardLayout>
              </ProtectedRoute>
            } />        <Route path="/dashboard/explore" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Explore />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            {/* Redirect old routes to new combined explore page */}
            <Route path="/dashboard/explore-creators" element={
              <Navigate to="/dashboard/explore" replace state={{ view: 'creators' }} />
            } />

            <Route path="/dashboard/explore-posts" element={
              <Navigate to="/dashboard/explore" replace state={{ view: 'posts' }} />
            } />

            <Route path="/dashboard/achievements" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Achievements />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            {/* New Academy route */}
            <Route path="/dashboard/academy" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Academy />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            {/* AI Tools Upload route - Admin only */}
            <Route path="/dashboard/ai-tools-upload" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <AIToolsUpload />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            {/* Messages route */}
            <Route path="/dashboard/messages" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Messages />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            {/* User Interactions route */}
            <Route path="/dashboard/interactions" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <UserInteractions />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            {/* Analytics route */}
            <Route path="/dashboard/analytics" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Analytics />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            <Route path="/dashboard/toolkits/:id" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <ToolkitDetail />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            <Route path="/dashboard/tools/:id" element={
              <ProtectedRoute>
                <DashboardLayout>
                  <ToolDetail />
                </DashboardLayout>
              </ProtectedRoute>
            } />

            <Route path="*" element={<Navigate to="/" replace />} />            </Routes>
        </PortfolioProvider>
      </BackgroundProvider>
    </QueryClientProvider>
  );
}

export default App;
