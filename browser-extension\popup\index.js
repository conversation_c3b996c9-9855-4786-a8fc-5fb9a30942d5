import { initializeApp } from 'firebase/app';
import { getAuth, signInWithPopup, GoogleAuthProvider, signOut } from 'firebase/auth';
import { getFirestore, collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { firebaseConfig } from '../shared/firebase-config.js';

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// DOM Elements
const loginButton = document.getElementById('login-button');
const logoutButton = document.getElementById('logout-button');
const userInfo = document.getElementById('user-info');
const userEmail = document.getElementById('user-email');
const authContainer = document.getElementById('auth-container');
const content = document.getElementById('content');
const tabButtons = document.querySelectorAll('.tab-button');
const tabContents = document.querySelectorAll('.tab-content');
const pageRecommendations = document.getElementById('page-recommendations');
const toolsList = document.getElementById('tools-list');
const searchInput = document.getElementById('search-tools');

// Check if user is already logged in
auth.onAuthStateChanged(user => {
  if (user) {
    showUserInfo(user);
    loadUserTools();
    getCurrentTab().then(tab => {
      if (tab) {
        getRecommendationsForPage(tab.url);
      }
    });
  } else {
    hideUserInfo();
  }
});

// Login with Google
loginButton.addEventListener('click', async () => {
  try {
    const provider = new GoogleAuthProvider();
    await signInWithPopup(auth, provider);
  } catch (error) {
    console.error('Login error:', error);
  }
});

// Logout
logoutButton.addEventListener('click', async () => {
  try {
    await signOut(auth);
  } catch (error) {
    console.error('Logout error:', error);
  }
});

// Tab switching
tabButtons.forEach(button => {
  button.addEventListener('click', () => {
    // Remove active class from all buttons and contents
    tabButtons.forEach(btn => btn.classList.remove('active'));
    tabContents.forEach(content => content.classList.remove('active'));

    // Add active class to clicked button and corresponding content
    button.classList.add('active');
    const tabId = button.dataset.tab;
    document.getElementById(tabId).classList.add('active');
  });
});

// Search functionality
searchInput.addEventListener('input', (e) => {
  const searchTerm = e.target.value.toLowerCase();
  const toolCards = toolsList.querySelectorAll('.tool-card');

  toolCards.forEach(card => {
    const toolName = card.querySelector('.tool-name').textContent.toLowerCase();
    if (toolName.includes(searchTerm)) {
      card.style.display = 'flex';
    } else {
      card.style.display = 'none';
    }
  });
});

// Helper Functions
function showUserInfo(user) {
  loginButton.classList.add('hidden');
  userInfo.classList.remove('hidden');
  userEmail.textContent = user.email;
  content.classList.remove('hidden');
}

function hideUserInfo() {
  loginButton.classList.remove('hidden');
  userInfo.classList.add('hidden');
  content.classList.add('hidden');
}

async function getCurrentTab() {
  return new Promise((resolve) => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      resolve(tabs[0]);
    });
  });
}

async function loadUserTools() {
  if (!auth.currentUser) return;

  toolsList.innerHTML = '<div class="loading">Loading your tools...</div>';

  try {
    // Fetch from tools collection
    const toolsRef = collection(db, 'tools');
    const q = query(toolsRef, where('user_id', '==', auth.currentUser.uid));
    const snapshot = await getDocs(q);
    const toolsFromCollection = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      source: 'tools_collection'
    }));

    // Fetch from user's toolTracker array
    const userRef = doc(db, 'users', auth.currentUser.uid);
    const userDoc = await getDoc(userRef);

    let toolsFromTracker = [];
    if (userDoc.exists()) {
      const userData = userDoc.data();
      const toolTracker = userData.toolTracker || [];

      // Convert toolTracker items to Tool format
      toolsFromTracker = toolTracker.map((tool) => {
        return {
          id: tool.id,
          name: tool.name,
          description: tool.useCase || tool.description || '',
          logo_url: tool.logoUrl || tool.logoLink,
          website_url: tool.website || tool.websiteLink,
          tags: tool.tags || [],
          source: 'user_tooltracker'
        };
      });
    }

    const allTools = [...toolsFromCollection, ...toolsFromTracker];

    if (allTools.length === 0) {
      toolsList.innerHTML = '<div class="loading">No tools found. Add some in SortMyAI!</div>';
      return;
    }

    renderTools(allTools, toolsList);
  } catch (error) {
    console.error('Error loading tools:', error);
    toolsList.innerHTML = '<div class="loading">Error loading tools. Please try again.</div>';
  }
}

async function getRecommendationsForPage(url) {
  pageRecommendations.innerHTML = '<div class="loading">Finding relevant tools...</div>';

  try {
    // Extract domain from URL
    const domain = new URL(url).hostname;

    // Get all AI tools
    const toolsCollection = collection(db, 'aiTools');
    const toolsSnapshot = await getDocs(toolsCollection);

    const allTools = toolsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // Simple recommendation algorithm based on page context
    // In a real implementation, this would be more sophisticated
    let recommendedTools = [];

    // Check if we're on a specific AI tool's website
    const currentToolSite = allTools.find(tool => {
      if (!tool.website) return false;
      try {
        const toolDomain = new URL(tool.website).hostname;
        return domain.includes(toolDomain) || toolDomain.includes(domain);
      } catch {
        return false;
      }
    });

    if (currentToolSite) {
      // If we're on an AI tool site, recommend alternatives
      const category = currentToolSite.category;
      const currentTags = Array.isArray(currentToolSite.tags) ? currentToolSite.tags : [];

      recommendedTools = allTools.filter(tool =>
        tool.id !== currentToolSite.id && (
          tool.category === category ||
          (Array.isArray(tool.tags) && tool.tags.some(tag => currentTags.includes(tag)))
        )
      ).slice(0, 6);
    } else {
      // Otherwise recommend popular tools or tools that might be relevant to the domain
      // This is a simplified version - a real implementation would be more sophisticated
      const keywords = domain.split('.');
      recommendedTools = allTools.filter(tool => {
        const toolName = tool.name.toLowerCase();
        const toolDesc = (tool.description || '').toLowerCase();
        return keywords.some(keyword =>
          toolName.includes(keyword) || toolDesc.includes(keyword)
        );
      });

      // If no matches, show popular tools
      if (recommendedTools.length < 3) {
        recommendedTools = allTools
          .sort(() => 0.5 - Math.random()) // Simple random sorting
          .slice(0, 6);
      }
    }

    if (recommendedTools.length === 0) {
      pageRecommendations.innerHTML = '<div class="loading">No relevant tools found for this page.</div>';
      return;
    }

    renderTools(recommendedTools, pageRecommendations);
  } catch (error) {
    console.error('Error getting recommendations:', error);
    pageRecommendations.innerHTML = '<div class="loading">Error loading recommendations.</div>';
  }
}

function renderTools(tools, container) {
  container.innerHTML = '';

  tools.forEach(tool => {
    const toolCard = document.createElement('div');
    toolCard.className = 'tool-card';
    toolCard.dataset.id = tool.id;

    const logoUrl = tool.logo_url || tool.logoUrl || tool.logoLink || '../icons/default-tool.png';
    const websiteUrl = tool.website_url || tool.website || tool.websiteLink;
    const firstLetter = tool.name.charAt(0).toUpperCase();

    // Create the iPhone-like icon layout
    toolCard.innerHTML = `
      <div class="tool-icon" data-first-letter="${firstLetter}">
        <img src="${logoUrl}" alt="${tool.name}" onerror="this.onerror=null; this.parentElement.innerHTML='<span style=\\'font-size:24px; font-weight:bold; color:white\\'>${firstLetter}</span>';">
      </div>
      <div class="tool-name">${tool.name}</div>
      <div class="tool-actions">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="external-link-icon">
          <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
          <polyline points="15 3 21 3 21 9"></polyline>
          <line x1="10" y1="14" x2="21" y2="3"></line>
        </svg>
      </div>
    `;

    // Open website when clicking on the tool icon
    toolCard.querySelector('.tool-icon').addEventListener('click', () => {
      if (websiteUrl) {
        chrome.tabs.create({ url: websiteUrl.startsWith('http') ? websiteUrl : `https://${websiteUrl}` });
      }
    });

    container.appendChild(toolCard);
  });
}