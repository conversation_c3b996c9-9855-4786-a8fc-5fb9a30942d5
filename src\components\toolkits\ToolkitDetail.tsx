import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Tool } from '@/types/tools';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Share, Upload, User as UserIcon } from 'lucide-react';
import { useToolkit } from '@/hooks/useToolkit';
import { useToast } from '@/hooks/use-toast';
import { ToolkitShareModal } from './ToolkitShareModal';

export default function ToolkitDetail() {
    const { id } = useParams();
    const { user: currentUser } = useAuth();
    const { toast } = useToast();
    const { toolkit, tools, creator, loading, error, fetchToolkit } = useToolkit();
    const [publishing, setPublishing] = useState(false);
    const [showShareModal, setShowShareModal] = useState(false);

    useEffect(() => {
        if (id) {
            fetchToolkit(id);
        }
    }, [id, fetchToolkit]);

    const handleShare = () => {
        if (!toolkit || !currentUser) return;
        setShowShareModal(true);
    };

    const handlePublish = async () => {
        if (!toolkit || !currentUser) return;
        setPublishing(true);
        try {
            // Implementation for publishing
            console.log('Publishing toolkit:', toolkit.id);
        } catch (error) {
            console.error('Error publishing toolkit:', error);
            toast({
                title: "Error",
                description: "Failed to publish toolkit",
                variant: "destructive"
            });
        } finally {
            setPublishing(false);
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center p-8">
                <Loader2 className="w-8 h-8 animate-spin" />
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center p-8">
                <p>Error: {error.message}</p>
            </div>
        );
    }

    if (!toolkit) {
        return (
            <div className="text-center p-8">
                <p>Toolkit not found</p>
            </div>
        );
    }

    const isOwner = currentUser?.uid === toolkit.created_by;

    return (
        <div className="container mx-auto p-6 space-y-6">
            <Card>
                <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                        <div>
                            <h1 className="text-2xl font-bold">{toolkit.name}</h1>
                            <p className="text-gray-500">{toolkit.description}</p>
                            {creator && (
                                <div className="flex items-center gap-2 mt-2 text-sm text-gray-500">
                                    <UserIcon className="w-4 h-4" />
                                    <span>Created by {creator.username}</span>
                                </div>
                            )}
                            {toolkit.tags && (
                                <div className="flex gap-2 mt-2">
                                    {toolkit.tags.map((tag: string) => (
                                        <span
                                            key={`${toolkit.id}-${tag}`}
                                            className="px-2 py-1 bg-blue-100 rounded text-sm"
                                        >
                                            {tag}
                                        </span>
                                    ))}
                                </div>
                            )}
                        </div>

                        <div className="flex gap-2">
                            {/* Share button - available to everyone */}
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={handleShare}
                            >
                                <Share className="w-4 h-4" />
                                <span className="ml-2">Share</span>
                            </Button>

                            {/* Owner-only actions */}
                            {isOwner && !toolkit.is_published && (
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handlePublish}
                                    disabled={publishing}
                                >
                                    {publishing ? (
                                        <Loader2 className="w-4 h-4 animate-spin" />
                                    ) : (
                                        <Upload className="w-4 h-4" />
                                    )}
                                    <span className="ml-2">Publish</span>
                                </Button>
                            )}
                        </div>
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardContent className="p-6">
                    <h2 className="text-xl font-semibold mb-4">Tools</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {tools.map((tool: Tool) => (
                            <Card key={tool.id}>
                                <CardContent className="p-4">
                                    <div className="flex items-center gap-3">
                                        {tool.logo_url && (
                                            <img
                                                src={tool.logo_url}
                                                alt={tool.name}
                                                className="w-10 h-10 rounded-lg object-cover"
                                            />
                                        )}
                                        <div>
                                            <h3 className="font-medium">{tool.name}</h3>
                                            <p className="text-sm text-gray-500">{tool.description}</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </CardContent>
            </Card>

            {/* Share Modal */}
            {toolkit && (
                <ToolkitShareModal
                    isOpen={showShareModal}
                    onClose={() => setShowShareModal(false)}
                    toolkit={toolkit}
                />
            )}
        </div>
    );
}
