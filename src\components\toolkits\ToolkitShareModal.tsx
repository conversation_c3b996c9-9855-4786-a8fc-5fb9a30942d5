import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import {
  Share,
  Copy,
  Users,
  UserPlus,
  Send,
  Facebook,
  Twitter,
  Linkedin,
  MessageCircle,
  Mail,
  Link,
  Check,
  Loader2
} from 'lucide-react';
import { User } from '@/types';
import { Toolkit } from '@/types/tools';
import { getFollowersWithDetails, getFollowingWithDetails } from '@/services/followService';
import { shareToolkit, shareToolkitWithFollowers } from '@/services/toolkitService';

interface ToolkitShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  toolkit: Toolkit;
}

export const ToolkitShareModal: React.FC<ToolkitShareModalProps> = ({
  isOpen,
  onClose,
  toolkit
}) => {
  const { user } = useAuth();
  const { toast } = useToast();

  const [followers, setFollowers] = useState<User[]>([]);
  const [following, setFollowing] = useState<User[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [directShareEmail, setDirectShareEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [sharing, setSharing] = useState(false);
  const [linkCopied, setLinkCopied] = useState(false);

  // Generate shareable link
  const shareableLink = `${window.location.origin}/dashboard/toolkits/${toolkit.id}`;

  useEffect(() => {
    if (isOpen && user) {
      fetchUserConnections();
    }
  }, [isOpen, user]);

  const fetchUserConnections = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const [followersData, followingData] = await Promise.all([
        getFollowersWithDetails(user.uid),
        getFollowingWithDetails(user.uid)
      ]);

      setFollowers(followersData);
      setFollowing(followingData);
    } catch (error) {
      console.error('Error fetching user connections:', error);
      toast({
        title: 'Error',
        description: 'Failed to load your connections',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleUserToggle = (userId: string) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleShareWithSelected = async () => {
    if (!user || selectedUsers.length === 0) return;

    setSharing(true);
    try {
      await Promise.all(
        selectedUsers.map(userId => shareToolkit(toolkit.id, userId))
      );

      toast({
        title: 'Success',
        description: `Toolkit shared with ${selectedUsers.length} user(s). They will receive notifications.`,
      });

      setSelectedUsers([]);
      onClose();
    } catch (error) {
      console.error('Error sharing toolkit:', error);
      toast({
        title: 'Error',
        description: 'Failed to share toolkit',
        variant: 'destructive'
      });
    } finally {
      setSharing(false);
    }
  };

  const handleShareWithAllFollowers = async () => {
    if (!user || followers.length === 0) return;

    setSharing(true);
    try {
      const followerIds = followers.map(f => f.uid || f.id);
      await shareToolkitWithFollowers(toolkit.id, followerIds);

      toast({
        title: 'Success',
        description: `Toolkit shared with all ${followers.length} followers. They will receive notifications.`,
      });

      onClose();
    } catch (error) {
      console.error('Error sharing with followers:', error);
      toast({
        title: 'Error',
        description: 'Failed to share with followers',
        variant: 'destructive'
      });
    } finally {
      setSharing(false);
    }
  };

  const handleDirectShare = async () => {
    if (!directShareEmail.trim()) return;

    // For now, just copy link and show message
    // In a real implementation, you might send an email or notification
    await copyToClipboard(shareableLink);
    toast({
      title: 'Link copied',
      description: `Share this link with ${directShareEmail}`,
    });
    setDirectShareEmail('');
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setLinkCopied(true);
      setTimeout(() => setLinkCopied(false), 2000);
      toast({
        title: 'Copied!',
        description: 'Link copied to clipboard',
      });
    } catch (error) {
      console.error('Failed to copy:', error);
      toast({
        title: 'Error',
        description: 'Failed to copy link',
        variant: 'destructive'
      });
    }
  };

  const handleSocialShare = (platform: string) => {
    const text = `Check out this amazing toolkit: ${toolkit.name}`;
    const url = shareableLink;

    let shareUrl = '';

    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
        break;
      case 'whatsapp':
        shareUrl = `https://wa.me/?text=${encodeURIComponent(`${text} ${url}`)}`;
        break;
      case 'email':
        shareUrl = `mailto:?subject=${encodeURIComponent(toolkit.name)}&body=${encodeURIComponent(`${text}\n\n${url}`)}`;
        break;
    }

    if (shareUrl) {
      window.open(shareUrl, '_blank', 'noopener,noreferrer');
    }
  };

  const UserList = ({ users, title }: { users: User[], title: string }) => (
    <div className="space-y-2">
      <h4 className="font-medium text-sm text-gray-600">{title} ({users.length})</h4>
      {users.length === 0 ? (
        <p className="text-sm text-gray-500">No {title.toLowerCase()} found</p>
      ) : (
        <div className="space-y-2 max-h-40 overflow-y-auto">
          {users.map((user) => {
            const userId = user.uid || user.id;
            const isSelected = selectedUsers.includes(userId);

            return (
              <div
                key={userId}
                className={`flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-colors ${isSelected ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'
                  }`}
                onClick={() => handleUserToggle(userId)}
              >
                <Avatar className="w-8 h-8">
                  <AvatarImage src={user.avatar_url} />
                  <AvatarFallback>
                    {user.username?.charAt(0)?.toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <p className="text-sm font-medium">{user.username || 'Unknown User'}</p>
                  {user.profession && (
                    <p className="text-xs text-gray-500">{user.profession}</p>
                  )}
                </div>
                {isSelected && (
                  <Check className="w-4 h-4 text-blue-600" />
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share className="w-5 h-5" />
            Share "{toolkit.name}"
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="internal" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="internal" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              Your Network
            </TabsTrigger>
            <TabsTrigger value="external" className="flex items-center gap-2">
              <Link className="w-4 h-4" />
              External Share
            </TabsTrigger>
          </TabsList>

          <TabsContent value="internal" className="space-y-4">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-6 h-6 animate-spin" />
              </div>
            ) : (
              <ScrollArea className="h-[400px] pr-4">
                <div className="space-y-6">
                  {/* Quick Actions */}
                  <div className="space-y-2">
                    <Button
                      onClick={handleShareWithAllFollowers}
                      disabled={sharing || followers.length === 0}
                      className="w-full"
                      variant="outline"
                    >
                      {sharing ? (
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      ) : (
                        <Users className="w-4 h-4 mr-2" />
                      )}
                      Share with All Followers ({followers.length})
                    </Button>
                  </div>

                  {/* Followers */}
                  <UserList users={followers} title="Followers" />

                  {/* Following */}
                  <UserList users={following} title="Following" />

                  {/* Selected Users Actions */}
                  {selectedUsers.length > 0 && (
                    <div className="border-t pt-4">
                      <div className="flex items-center justify-between">
                        <Badge variant="secondary">
                          {selectedUsers.length} user(s) selected
                        </Badge>
                        <Button
                          onClick={handleShareWithSelected}
                          disabled={sharing}
                          size="sm"
                        >
                          {sharing ? (
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          ) : (
                            <Send className="w-4 h-4 mr-2" />
                          )}
                          Share
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </ScrollArea>
            )}
          </TabsContent>

          <TabsContent value="external" className="space-y-4">
            <div className="space-y-6">
              {/* Copy Link */}
              <div className="space-y-2">
                <Label>Share Link</Label>
                <div className="flex gap-2">
                  <Input
                    value={shareableLink}
                    readOnly
                    className="flex-1"
                  />
                  <Button
                    onClick={() => copyToClipboard(shareableLink)}
                    variant="outline"
                    size="sm"
                  >
                    {linkCopied ? (
                      <Check className="w-4 h-4" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Direct Share */}
              <div className="space-y-2">
                <Label>Share with Email</Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Enter email address"
                    value={directShareEmail}
                    onChange={(e) => setDirectShareEmail(e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    onClick={handleDirectShare}
                    disabled={!directShareEmail.trim()}
                    variant="outline"
                    size="sm"
                  >
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Social Media */}
              <div className="space-y-2">
                <Label>Share on Social Media</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    onClick={() => handleSocialShare('twitter')}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Twitter className="w-4 h-4" />
                    Twitter
                  </Button>
                  <Button
                    onClick={() => handleSocialShare('facebook')}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Facebook className="w-4 h-4" />
                    Facebook
                  </Button>
                  <Button
                    onClick={() => handleSocialShare('linkedin')}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Linkedin className="w-4 h-4" />
                    LinkedIn
                  </Button>
                  <Button
                    onClick={() => handleSocialShare('whatsapp')}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <MessageCircle className="w-4 h-4" />
                    WhatsApp
                  </Button>
                  <Button
                    onClick={() => handleSocialShare('email')}
                    variant="outline"
                    className="flex items-center gap-2 col-span-2"
                  >
                    <Mail className="w-4 h-4" />
                    Email
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
