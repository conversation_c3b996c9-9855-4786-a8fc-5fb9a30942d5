[build]
  publish = "dist"
  command = "npm run build"

[[headers]]
  for = "/*"
  [headers.values]
    Cross-Origin-Embedder-Policy = "unsafe-none"
    Cross-Origin-Opener-Policy = "same-origin-allow-popups"
    Cross-Origin-Resource-Policy = "cross-origin"
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization, X-Requested-With"
    Access-Control-Allow-Credentials = "true"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
