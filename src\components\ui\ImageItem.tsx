import React, { useState, useEffect, useCallback } from 'react';
import { Image } from 'lucide-react';
import { cn } from '@/lib/utils';
import { getStorage, ref, getDownloadURL } from 'firebase/storage';
import { getGoogleDriveFileId, getGoogleDriveImageUrls, isGoogleDriveUrl } from '@/utils/googleDriveUtils';

interface ImageItemProps {
  src: string;
  alt: string;
  className?: string;
}

export const ImageItem: React.FC<ImageItemProps> = ({ src, alt, className }) => {
  const [imgError, setImgError] = useState(false);
  const [errorAttempts, setErrorAttempts] = useState(0);
  const [imgSrc, setImgSrc] = useState(src);

  useEffect(() => {
    setImgSrc(src);
    setImgError(false);
    setErrorAttempts(0);
  }, [src]);

  const handleImageError = useCallback(() => {
    // Track error attempts and set error state
    setErrorAttempts(prev => prev + 1);
    setImgError(true);

    // We'll skip trying different approaches since they're likely to fail due to COEP restrictions
    // Instead, we'll just let the fallback UI show up after a single error

    // Only try Firebase Storage URLs
    if (src.includes('firebasestorage.googleapis.com')) {
      // For Firebase Storage URLs, try adding a token
      try {
        const storageRef = ref(getStorage(), src);
        getDownloadURL(storageRef)
          .then(url => setImgSrc(url))
          .catch(console.error);
      } catch (error) {
        console.error('Error getting download URL:', error);
      }
    }
  }, [src, imgError, errorAttempts]);

  // Show fallback UI after errors for Google Drive images
  if (imgError && isGoogleDriveUrl(src)) {
    const fileId = getGoogleDriveFileId(src);

    if (fileId) {
      const urls = getGoogleDriveImageUrls(fileId);
      return (
        <div className={cn("h-full flex flex-col items-center justify-center bg-gray-800/50 p-4 text-center", className)}>
          <Image className="w-12 h-12 text-gray-400 mb-2" />
          <p className="text-sm text-gray-300 mb-3">Image preview unavailable</p>
          <p className="text-xs text-gray-400 mb-3">This may be due to permission settings or CORS restrictions.</p>
          <a
            href={urls.view}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-400 hover:text-blue-300 text-sm underline"
          >
            View in Google Drive
          </a>
        </div>
      );
    }
    return (
      <div className={cn("h-full flex items-center justify-center bg-gray-800/50", className)}>
        <Image className="w-16 h-16 text-gray-500" />
      </div>
    );
  }

  // For Google Drive images, try multiple URL formats
  if (isGoogleDriveUrl(src)) {
    const fileId = getGoogleDriveFileId(src);

    if (fileId) {
      const urls = getGoogleDriveImageUrls(fileId);
      return (
        <div className={cn("h-full overflow-hidden", className)}>
          <img
            src={urls.primary}
            alt={alt}
            className="w-full h-full object-cover"
            onError={(e) => {
              // If this fails, try the thumbnail format
              const target = e.target as HTMLImageElement;
              target.onerror = () => {
                // If thumbnail also fails, trigger the main error handler
                handleImageError();
              };
              target.src = urls.fallback;
            }}
            crossOrigin="anonymous"
            referrerPolicy="no-referrer"
          />
        </div>
      );
    }
  }

  // For all other images, use the standard img tag
  return (
    <div
      className={cn("h-full overflow-hidden", className)}
      style={{ contain: 'paint' }}
    >
      <img
        src={imgSrc}
        alt={alt}
        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
        loading="lazy"
        onError={handleImageError}
        referrerPolicy="no-referrer"
      />
    </div>
  );
};

export default ImageItem;
