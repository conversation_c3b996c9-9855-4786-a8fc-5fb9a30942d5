// Helper function to check if a URL is a Livepeer playback URL
export function isLivepeerUrl(url: string): boolean {
    return url.includes('livepeer.studio') || url.includes('livepeer.com');
}

// Helper function to extract video ID from Livepeer URL
export function getLivepeerVideoId(url: string): string | null {
    try {
        const urlObj = new URL(url);
        if (urlObj.hostname.includes('livepeer')) {
            // Extract the video ID from the path
            const pathParts = urlObj.pathname.split('/');
            return pathParts[pathParts.length - 1];
        }
        return null;
    } catch {
        return null;
    }
}
