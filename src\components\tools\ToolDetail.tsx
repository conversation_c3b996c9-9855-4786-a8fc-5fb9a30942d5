import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { doc, getDoc, collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Tool } from '@/types/tools';
import { AITool } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Loader2, 
  ArrowLeft, 
  ExternalLink, 
  Globe, 
  DollarSign, 
  Star,
  Calendar,
  User,
  Tag,
  Zap,
  Edit,
  Trash2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ToolDetailData extends Partial<Tool>, Partial<AITool> {
  id: string;
  name: string;
  description: string;
  source: 'tools' | 'aiTools';
}

export default function ToolDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user, isAdmin } = useAuth();
  const { toast } = useToast();
  
  const [tool, setTool] = useState<ToolDetailData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTool = async () => {
      if (!id) return;
      
      setLoading(true);
      setError(null);
      
      try {
        // Try to fetch from tools collection first
        const toolDoc = await getDoc(doc(db, 'tools', id));
        
        if (toolDoc.exists()) {
          const toolData = { id: toolDoc.id, ...toolDoc.data(), source: 'tools' as const } as ToolDetailData;
          setTool(toolData);
        } else {
          // Try to fetch from aiTools collection
          const aiToolDoc = await getDoc(doc(db, 'aiTools', id));
          
          if (aiToolDoc.exists()) {
            const aiToolData = { id: aiToolDoc.id, ...aiToolDoc.data(), source: 'aiTools' as const } as ToolDetailData;
            setTool(aiToolData);
          } else {
            setError('Tool not found');
          }
        }
      } catch (err) {
        console.error('Error fetching tool:', err);
        setError('Failed to load tool details');
      } finally {
        setLoading(false);
      }
    };

    fetchTool();
  }, [id]);

  const handleEdit = () => {
    if (!tool) return;
    
    if (tool.source === 'tools') {
      // Navigate to edit tool page (you may need to create this)
      navigate(`/dashboard/tools/edit/${tool.id}`);
    } else {
      // Navigate to AI tools upload page for editing
      navigate(`/dashboard/ai-tools-upload?edit=${tool.id}`);
    }
  };

  const handleDelete = async () => {
    if (!tool || !user) return;
    
    // Add confirmation dialog and delete logic here
    toast({
      title: "Delete functionality",
      description: "Delete functionality will be implemented here",
    });
  };

  const handleVisitWebsite = () => {
    if (!tool) return;
    
    const websiteUrl = tool.website || tool.websiteLink || tool.link;
    if (websiteUrl) {
      window.open(websiteUrl, '_blank', 'noopener,noreferrer');
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return 'Unknown';
    }
  };

  const formatTags = (tags: string | string[] | undefined): string[] => {
    if (!tags) return [];
    if (Array.isArray(tags)) return tags;
    if (typeof tags === 'string') return tags.split(',').map(tag => tag.trim());
    return [];
  };

  const getPricingColor = (pricing?: string) => {
    switch (pricing?.toLowerCase()) {
      case 'free': return 'bg-green-100 text-green-800';
      case 'freemium': return 'bg-blue-100 text-blue-800';
      case 'paid': return 'bg-orange-100 text-orange-800';
      case 'subscription': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (error || !tool) {
    return (
      <div className="text-center p-8">
        <p className="text-red-500 mb-4">{error || 'Tool not found'}</p>
        <Button onClick={() => navigate(-1)} variant="outline">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Go Back
        </Button>
      </div>
    );
  }

  const canEdit = user && (isAdmin || tool.user_id === user.uid || tool.createdBy === user.uid);
  const websiteUrl = tool.website || tool.websiteLink || tool.link;
  const logoUrl = tool.logo_url || tool.logoUrl;
  const tags = formatTags(tool.tags);

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button onClick={() => navigate(-1)} variant="outline" size="sm">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">Tool Details</h1>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Tool Header */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                {logoUrl && (
                  <img
                    src={logoUrl}
                    alt={tool.name}
                    className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
                  />
                )}
                <div className="flex-1">
                  <div className="flex items-start justify-between">
                    <div>
                      <h2 className="text-3xl font-bold mb-2">{tool.name}</h2>
                      <p className="text-gray-600 text-lg leading-relaxed">
                        {tool.description}
                      </p>
                    </div>
                    {canEdit && (
                      <div className="flex gap-2">
                        <Button onClick={handleEdit} variant="outline" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button onClick={handleDelete} variant="outline" size="sm">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Use Case & Excellence */}
          {(tool.useCase || tool.excelsAt) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  What it does
                </CardTitle>
              </CardHeader>
              <CardContent>
                {tool.useCase && (
                  <div className="mb-4">
                    <h4 className="font-semibold mb-2">Use Case</h4>
                    <p className="text-gray-600">{tool.useCase}</p>
                  </div>
                )}
                {tool.excelsAt && (
                  <div>
                    <h4 className="font-semibold mb-2">Excels At</h4>
                    <p className="text-gray-600">{tool.excelsAt}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Tags */}
          {tags.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Tag className="w-5 h-5" />
                  Tags
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {websiteUrl && (
                <Button onClick={handleVisitWebsite} className="w-full" size="lg">
                  <Globe className="w-4 h-4 mr-2" />
                  Visit Website
                </Button>
              )}
              <Button variant="outline" className="w-full">
                <Star className="w-4 h-4 mr-2" />
                Add to Favorites
              </Button>
            </CardContent>
          </Card>

          {/* Tool Info */}
          <Card>
            <CardHeader>
              <CardTitle>Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {tool.pricing && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Pricing</span>
                  <Badge className={getPricingColor(tool.pricing)}>
                    {tool.pricing}
                  </Badge>
                </div>
              )}
              
              {tool.category && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Category</span>
                  <Badge variant="outline">{tool.category}</Badge>
                </div>
              )}

              {tool.created_at && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Added</span>
                  <span className="text-sm text-gray-600">
                    {formatDate(tool.created_at)}
                  </span>
                </div>
              )}

              {tool.source && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Source</span>
                  <Badge variant="outline">
                    {tool.source === 'aiTools' ? 'AI Tools Library' : 'User Tools'}
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Free Credits */}
          {tool.freeCredits && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="w-5 h-5" />
                  Free Credits
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">{tool.freeCredits}</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
