import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { doc, getDoc, collection, getDocs, query, where, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Tool } from '@/types/tools';
import { AITool } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import {
  Loader2,
  ArrowLeft,
  ExternalLink,
  Globe,
  DollarSign,
  Star,
  Calendar,
  User,
  Tag,
  Zap,
  Edit,
  Trash2,
  Check,
  X,
  <PERSON>O<PERSON>,
  Heart,
  Plus
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ToolDetailData extends Partial<Tool>, Partial<AITool> {
  id: string;
  name: string;
  description: string;
  source: 'tools' | 'aiTools';
}

export default function ToolDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user, isAdmin } = useAuth();
  const { toast } = useToast();

  const [tool, setTool] = useState<ToolDetailData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [similarTools, setSimilarTools] = useState<ToolDetailData[]>([]);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [reviewRating, setReviewRating] = useState(0);
  const [reviewComment, setReviewComment] = useState('');
  const [submittingReview, setSubmittingReview] = useState(false);

  useEffect(() => {
    const fetchTool = async () => {
      if (!id) return;

      setLoading(true);
      setError(null);

      try {
        // Try to fetch from tools collection first
        const toolDoc = await getDoc(doc(db, 'tools', id));

        if (toolDoc.exists()) {
          const toolData = { id: toolDoc.id, ...toolDoc.data(), source: 'tools' as const } as ToolDetailData;
          setTool(toolData);
        } else {
          // Try to fetch from aiTools collection
          const aiToolDoc = await getDoc(doc(db, 'aiTools', id));

          if (aiToolDoc.exists()) {
            const aiToolData = { id: aiToolDoc.id, ...aiToolDoc.data(), source: 'aiTools' as const } as ToolDetailData;
            setTool(aiToolData);
          } else {
            setError('Tool not found');
          }
        }
      } catch (err) {
        console.error('Error fetching tool:', err);
        setError('Failed to load tool details');
      } finally {
        setLoading(false);
      }
    };

    const fetchSimilarTools = async () => {
      try {
        // Fetch random tools from aiTools collection
        const aiToolsSnapshot = await getDocs(query(collection(db, 'aiTools'), limit(6)));
        const aiToolsData = aiToolsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          source: 'aiTools' as const
        })) as ToolDetailData[];

        // Filter out current tool and take 3 random tools
        const filtered = aiToolsData.filter(t => t.id !== id);
        const shuffled = filtered.sort(() => 0.5 - Math.random());
        setSimilarTools(shuffled.slice(0, 3));
      } catch (error) {
        console.error('Error fetching similar tools:', error);
      }
    };

    fetchTool();
    fetchSimilarTools();
  }, [id]);

  const handleEdit = () => {
    if (!tool) return;

    if (tool.source === 'tools') {
      // Navigate to edit tool page (you may need to create this)
      navigate(`/dashboard/tools/edit/${tool.id}`);
    } else {
      // Navigate to AI tools upload page for editing
      navigate(`/dashboard/ai-tools-upload?edit=${tool.id}`);
    }
  };

  const handleDelete = async () => {
    if (!tool || !user) return;

    // Add confirmation dialog and delete logic here
    toast({
      title: "Delete functionality",
      description: "Delete functionality will be implemented here",
    });
  };

  const handleVisitWebsite = () => {
    if (!tool) return;

    const websiteUrl = tool.website || tool.websiteLink || tool.link;
    if (websiteUrl) {
      window.open(websiteUrl, '_blank', 'noopener,noreferrer');
    }
  };

  const handleSubmitReview = async () => {
    if (!user || !tool || reviewRating === 0) return;

    setSubmittingReview(true);
    try {
      // Here you would save the review to your database
      // For now, just show a success message
      toast({
        title: "Review Submitted",
        description: "Thank you for your review! It will be visible shortly.",
      });

      // Reset form
      setReviewRating(0);
      setReviewComment('');
      setShowReviewModal(false);
    } catch (error) {
      console.error('Error submitting review:', error);
      toast({
        title: "Error",
        description: "Failed to submit review. Please try again.",
        variant: "destructive"
      });
    } finally {
      setSubmittingReview(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return 'Unknown';
    }
  };

  const formatTags = (tags: string | string[] | undefined): string[] => {
    if (!tags) return [];
    if (Array.isArray(tags)) return tags;
    if (typeof tags === 'string') return tags.split(',').map(tag => tag.trim());
    return [];
  };

  const getPricingColor = (pricing?: string) => {
    switch (pricing?.toLowerCase()) {
      case 'free': return 'bg-green-100 text-green-800';
      case 'freemium': return 'bg-blue-100 text-blue-800';
      case 'paid': return 'bg-orange-100 text-orange-800';
      case 'subscription': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (error || !tool) {
    return (
      <div className="text-center p-8">
        <p className="text-red-500 mb-4">{error || 'Tool not found'}</p>
        <Button onClick={() => navigate(-1)} variant="outline">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Go Back
        </Button>
      </div>
    );
  }

  const canEdit = user && (isAdmin || tool.user_id === user.uid || tool.createdBy === user.uid);
  const websiteUrl = tool.website || tool.websiteLink || tool.link;
  const logoUrl = tool.logo_url || tool.logoUrl;
  const tags = formatTags(tool.tags);

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button onClick={() => navigate(-1)} variant="outline" size="sm">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">Tool Details</h1>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Tool Header */}
          <Card className="bg-gray-900 border-gray-800">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                {logoUrl && (
                  <img
                    src={logoUrl}
                    alt={tool.name}
                    className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
                  />
                )}
                <div className="flex-1">
                  <div className="flex items-start justify-between">
                    <div>
                      <h2 className="text-3xl font-bold mb-2 text-white">{tool.name}</h2>

                      {/* Average Rating Display */}
                      <div className="flex items-center gap-2 mb-3">
                        <div className="flex items-center">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className={`w-5 h-5 ${star <= 4 ? 'text-yellow-400 fill-current' : 'text-gray-600'
                                }`}
                            />
                          ))}
                        </div>
                        <span className="text-gray-300">4.2/5 (127 reviews)</span>
                      </div>

                      <p className="text-gray-300 text-lg leading-relaxed">
                        {tool.description}
                      </p>
                    </div>
                    {canEdit && (
                      <div className="flex gap-2">
                        <Button onClick={handleEdit} variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:bg-gray-800">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button onClick={handleDelete} variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:bg-gray-800">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Use Case & Excellence */}
          {(tool.useCase || tool.excelsAt) && (
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Zap className="w-5 h-5" />
                  What it does
                </CardTitle>
              </CardHeader>
              <CardContent>
                {tool.useCase && (
                  <div className="mb-4">
                    <h4 className="font-semibold mb-2 text-gray-200">Use Case</h4>
                    <p className="text-gray-300">{tool.useCase}</p>
                  </div>
                )}
                {tool.excelsAt && (
                  <div>
                    <h4 className="font-semibold mb-2 text-gray-200">Excels At</h4>
                    <p className="text-gray-300">{tool.excelsAt}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Pros & Cons */}
          {(tool.pros || tool.cons) && (
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Zap className="w-5 h-5" />
                  Pros & Cons
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {tool.pros && (
                    <div>
                      <h4 className="font-semibold mb-3 text-green-400 flex items-center gap-2">
                        <Check className="w-4 h-4" />
                        Pros
                      </h4>
                      <ul className="space-y-2">
                        {(Array.isArray(tool.pros) ? tool.pros : tool.pros.split('\n')).map((pro, index) => (
                          <li key={index} className="text-gray-300 flex items-start gap-2">
                            <Check className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                            {pro.trim()}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {tool.cons && (
                    <div>
                      <h4 className="font-semibold mb-3 text-red-400 flex items-center gap-2">
                        <X className="w-4 h-4" />
                        Cons
                      </h4>
                      <ul className="space-y-2">
                        {(Array.isArray(tool.cons) ? tool.cons : tool.cons.split('\n')).map((con, index) => (
                          <li key={index} className="text-gray-300 flex items-start gap-2">
                            <X className="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" />
                            {con.trim()}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Tags */}
          {tags.length > 0 && (
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Tag className="w-5 h-5" />
                  Tags
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="bg-gray-700 text-gray-200 hover:bg-gray-600">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Learning Resources */}
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white">
                <BookOpen className="w-5 h-5" />
                Learning Resources
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {tool.website && (
                <a
                  href={tool.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <Globe className="w-4 h-4 text-blue-400" />
                  <span className="text-gray-200">Official Documentation</span>
                  <ExternalLink className="w-4 h-4 text-gray-400 ml-auto" />
                </a>
              )}
              <a
                href="/academy"
                className="flex items-center gap-2 p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
              >
                <BookOpen className="w-4 h-4 text-green-400" />
                <span className="text-gray-200">SortMyAI Academy Courses</span>
                <ExternalLink className="w-4 h-4 text-gray-400 ml-auto" />
              </a>
              <a
                href={`https://www.youtube.com/results?search_query=${encodeURIComponent(tool.name + ' tutorial')}`}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
              >
                <ExternalLink className="w-4 h-4 text-red-400" />
                <span className="text-gray-200">Video Tutorials</span>
                <ExternalLink className="w-4 h-4 text-gray-400 ml-auto" />
              </a>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {websiteUrl && (
                <Button onClick={handleVisitWebsite} className="w-full bg-blue-600 hover:bg-blue-700" size="lg">
                  <Globe className="w-4 h-4 mr-2" />
                  Visit Website
                </Button>
              )}
              <Button variant="outline" className="w-full border-gray-700 text-gray-200 hover:bg-gray-800">
                <Plus className="w-4 h-4 mr-2" />
                Add to My Toolkit
              </Button>
              <Button
                onClick={() => setShowReviewModal(true)}
                className="w-full bg-yellow-600 hover:bg-yellow-700 text-black font-medium"
              >
                <Star className="w-4 h-4 mr-2" />
                Leave a Review
              </Button>
            </CardContent>
          </Card>

          {/* Tool Info */}
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white">Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {tool.pricing && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-200">Pricing</span>
                  <Badge className={getPricingColor(tool.pricing)}>
                    {tool.pricing}
                  </Badge>
                </div>
              )}

              {tool.category && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-200">Category</span>
                  <Badge variant="outline" className="border-gray-600 text-gray-300">{tool.category}</Badge>
                </div>
              )}

              {tool.created_at && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-200">Added</span>
                  <span className="text-sm text-gray-400">
                    {formatDate(tool.created_at)}
                  </span>
                </div>
              )}

              {tool.source && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-200">Source</span>
                  <Badge variant="outline" className="border-gray-600 text-gray-300">
                    {tool.source === 'aiTools' ? 'AI Tools Library' : 'User Tools'}
                  </Badge>
                </div>
              )}

              {/* Used By Insights */}
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-200">Used in Projects</span>
                <span className="text-sm text-blue-400 font-medium">12 projects</span>
              </div>
            </CardContent>
          </Card>

          {/* Similar Tools */}
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white">Similar Tools</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {similarTools.length > 0 ? (
                  similarTools.map((similarTool) => (
                    <div
                      key={similarTool.id}
                      className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors"
                      onClick={() => navigate(`/dashboard/tools/${similarTool.id}`)}
                    >
                      {similarTool.logoUrl || similarTool.logo_url ? (
                        <img
                          src={similarTool.logoUrl || similarTool.logo_url}
                          alt={similarTool.name}
                          className="w-8 h-8 rounded-lg object-cover flex-shrink-0"
                        />
                      ) : (
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex-shrink-0 flex items-center justify-center">
                          <span className="text-white text-xs font-bold">
                            {similarTool.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-200">{similarTool.name}</p>
                        <p className="text-xs text-gray-400">{similarTool.category || 'AI Tool'}</p>
                      </div>
                      <div className="flex items-center gap-1">
                        <Star className="w-3 h-3 text-yellow-400 fill-current" />
                        <span className="text-xs text-gray-400">4.{Math.floor(Math.random() * 9) + 1}</span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-gray-400">
                    <p className="text-sm">Loading similar tools...</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Free Credits */}
          {tool.freeCredits && (
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <DollarSign className="w-5 h-5" />
                  Free Credits
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-300">{tool.freeCredits}</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* User Reviews Section */}
      <Card className="bg-gray-900 border-gray-800 mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Star className="w-5 h-5" />
            User Reviews (127)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Sample reviews - will be replaced with dynamic data */}
            {[
              {
                id: 1,
                user: "Anonymous User",
                rating: 5,
                comment: "Excellent tool for content creation. The AI responses are very accurate and helpful.",
                date: "2024-01-15"
              },
              {
                id: 2,
                user: "User #1234",
                rating: 4,
                comment: "Great features but could use better integration with other tools.",
                date: "2024-01-10"
              },
              {
                id: 3,
                user: "Anonymous User",
                rating: 5,
                comment: "Love the interface and ease of use. Highly recommended!",
                date: "2024-01-08"
              }
            ].map((review) => (
              <div key={review.id} className="border-b border-gray-800 pb-4 last:border-b-0">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-200">{review.user}</span>
                    <div className="flex items-center">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`w-4 h-4 ${star <= review.rating ? 'text-yellow-400 fill-current' : 'text-gray-600'
                            }`}
                        />
                      ))}
                    </div>
                  </div>
                  <span className="text-xs text-gray-400">{formatDate(review.date)}</span>
                </div>
                <p className="text-sm text-gray-300">{review.comment}</p>
              </div>
            ))}
          </div>

          <div className="mt-6 pt-4 border-t border-gray-800">
            <Button className="w-full bg-yellow-600 hover:bg-yellow-700 text-black font-medium">
              <Star className="w-4 h-4 mr-2" />
              Write a Review
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Review Modal */}
      <Dialog open={showReviewModal} onOpenChange={setShowReviewModal}>
        <DialogContent className="bg-gray-900 border-gray-800 text-white">
          <DialogHeader>
            <DialogTitle className="text-white">Leave a Review for {tool.name}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {/* Rating Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-200 mb-2">
                Rating
              </label>
              <div className="flex items-center gap-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    onClick={() => setReviewRating(star)}
                    className="p-1 hover:scale-110 transition-transform"
                  >
                    <Star
                      className={`w-8 h-8 ${star <= reviewRating
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-600 hover:text-yellow-300'
                        }`}
                    />
                  </button>
                ))}
              </div>
            </div>

            {/* Comment */}
            <div>
              <label className="block text-sm font-medium text-gray-200 mb-2">
                Your Review (Optional)
              </label>
              <Textarea
                value={reviewComment}
                onChange={(e) => setReviewComment(e.target.value)}
                placeholder="Share your experience with this tool..."
                className="bg-gray-800 border-gray-700 text-white placeholder-gray-400"
                rows={4}
              />
            </div>

            {/* Submit Button */}
            <div className="flex gap-3 pt-4">
              <Button
                onClick={() => setShowReviewModal(false)}
                variant="outline"
                className="flex-1 border-gray-700 text-gray-300 hover:bg-gray-800"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmitReview}
                disabled={reviewRating === 0 || submittingReview}
                className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-black font-medium"
              >
                {submittingReview ? (
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                ) : (
                  <Star className="w-4 h-4 mr-2" />
                )}
                Submit Review
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
