import { db } from '@/lib/firebase';
import { 
  collection, 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  increment, 
  arrayUnion, 
  arrayRemove,
  onSnapshot,
  Unsubscribe
} from 'firebase/firestore';

export interface LikeData {
  totalLikes: number;
  totalViews: number;
  likedBy: string[];
  lastUpdated: string;
}

// Get likes and views for a toolkit or tool
export const getLikesAndViews = async (itemId: string, type: 'toolkit' | 'tool'): Promise<LikeData> => {
  try {
    const collectionName = type === 'toolkit' ? 'toolkitLikes' : 'toolLikes';
    const docRef = doc(db, collectionName, itemId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return docSnap.data() as LikeData;
    } else {
      // Initialize if doesn't exist
      const initialData: LikeData = {
        totalLikes: 0,
        totalViews: 0,
        likedBy: [],
        lastUpdated: new Date().toISOString()
      };
      await setDoc(docRef, initialData);
      return initialData;
    }
  } catch (error) {
    console.error('Error getting likes and views:', error);
    return {
      totalLikes: 0,
      totalViews: 0,
      likedBy: [],
      lastUpdated: new Date().toISOString()
    };
  }
};

// Toggle like for a toolkit or tool
export const toggleLike = async (
  itemId: string, 
  userId: string, 
  type: 'toolkit' | 'tool'
): Promise<{ isLiked: boolean; totalLikes: number }> => {
  try {
    const collectionName = type === 'toolkit' ? 'toolkitLikes' : 'toolLikes';
    const docRef = doc(db, collectionName, itemId);
    const docSnap = await getDoc(docRef);
    
    let currentData: LikeData;
    if (docSnap.exists()) {
      currentData = docSnap.data() as LikeData;
    } else {
      currentData = {
        totalLikes: 0,
        totalViews: 0,
        likedBy: [],
        lastUpdated: new Date().toISOString()
      };
    }
    
    const isCurrentlyLiked = currentData.likedBy.includes(userId);
    
    if (isCurrentlyLiked) {
      // Remove like
      await updateDoc(docRef, {
        totalLikes: increment(-1),
        likedBy: arrayRemove(userId),
        lastUpdated: new Date().toISOString()
      });
      return { isLiked: false, totalLikes: currentData.totalLikes - 1 };
    } else {
      // Add like
      await updateDoc(docRef, {
        totalLikes: increment(1),
        likedBy: arrayUnion(userId),
        lastUpdated: new Date().toISOString()
      });
      return { isLiked: true, totalLikes: currentData.totalLikes + 1 };
    }
  } catch (error) {
    console.error('Error toggling like:', error);
    throw error;
  }
};

// Increment view count
export const incrementViews = async (itemId: string, type: 'toolkit' | 'tool'): Promise<number> => {
  try {
    const collectionName = type === 'toolkit' ? 'toolkitLikes' : 'toolLikes';
    const docRef = doc(db, collectionName, itemId);
    
    // Check if document exists
    const docSnap = await getDoc(docRef);
    if (!docSnap.exists()) {
      // Initialize if doesn't exist
      const initialData: LikeData = {
        totalLikes: 0,
        totalViews: 1,
        likedBy: [],
        lastUpdated: new Date().toISOString()
      };
      await setDoc(docRef, initialData);
      return 1;
    } else {
      // Increment views
      await updateDoc(docRef, {
        totalViews: increment(1),
        lastUpdated: new Date().toISOString()
      });
      const currentData = docSnap.data() as LikeData;
      return currentData.totalViews + 1;
    }
  } catch (error) {
    console.error('Error incrementing views:', error);
    return 0;
  }
};

// Subscribe to real-time updates for likes and views
export const subscribeLikesAndViews = (
  itemId: string, 
  type: 'toolkit' | 'tool',
  callback: (data: LikeData) => void
): Unsubscribe => {
  const collectionName = type === 'toolkit' ? 'toolkitLikes' : 'toolLikes';
  const docRef = doc(db, collectionName, itemId);
  
  return onSnapshot(docRef, (doc) => {
    if (doc.exists()) {
      callback(doc.data() as LikeData);
    } else {
      // Initialize if doesn't exist
      const initialData: LikeData = {
        totalLikes: 0,
        totalViews: 0,
        likedBy: [],
        lastUpdated: new Date().toISOString()
      };
      setDoc(docRef, initialData);
      callback(initialData);
    }
  });
};

// Check if user has liked an item
export const checkUserLiked = async (
  itemId: string, 
  userId: string, 
  type: 'toolkit' | 'tool'
): Promise<boolean> => {
  try {
    const data = await getLikesAndViews(itemId, type);
    return data.likedBy.includes(userId);
  } catch (error) {
    console.error('Error checking user liked:', error);
    return false;
  }
};
