import { db } from '@/lib/firebase';
import { collection, doc, getDoc, getDocs, addDoc, updateDoc, deleteDoc, query, where, arrayUnion, arrayRemove } from 'firebase/firestore';
import type { Toolkit } from '@/types/tools';

export const toolkitCollection = collection(db, 'toolkits');

export const createToolkit = async (toolkit: Partial<Toolkit>): Promise<string> => {
    try {
        const docRef = await addDoc(toolkitCollection, {
            ...toolkit,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        });
        return docRef.id;
    } catch (error) {
        console.error('Error creating toolkit:', error);
        throw error;
    }
};

export const updateToolkit = async (id: string, toolkit: Partial<Toolkit>): Promise<void> => {
    try {
        const docRef = doc(toolkitCollection, id);
        await updateDoc(docRef, {
            ...toolkit,
            updated_at: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error updating toolkit:', error);
        throw error;
    }
};

export const deleteToolkit = async (id: string): Promise<void> => {
    try {
        await deleteDoc(doc(toolkitCollection, id));
    } catch (error) {
        console.error('Error deleting toolkit:', error);
        throw error;
    }
};

export const getToolkit = async (id: string): Promise<Toolkit | null> => {
    try {
        const docRef = doc(toolkitCollection, id);
        const docSnap = await getDoc(docRef);
        return docSnap.exists() ? { id: docSnap.id, ...docSnap.data() } as Toolkit : null;
    } catch (error) {
        console.error('Error getting toolkit:', error);
        throw error;
    }
};

export const getUserToolkits = async (userId: string): Promise<Toolkit[]> => {
    try {
        const q = query(toolkitCollection, where('created_by', '==', userId));
        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Toolkit));
    } catch (error) {
        console.error('Error getting user toolkits:', error);
        throw error;
    }
};

export const shareToolkit = async (toolkitId: string, targetUserId: string): Promise<void> => {
    try {
        const docRef = doc(toolkitCollection, toolkitId);
        await updateDoc(docRef, {
            shared_with: arrayUnion(targetUserId)
        });
    } catch (error) {
        console.error('Error sharing toolkit:', error);
        throw error;
    }
};

export const unshareToolkit = async (toolkitId: string, targetUserId: string): Promise<void> => {
    try {
        const docRef = doc(toolkitCollection, toolkitId);
        await updateDoc(docRef, {
            shared_with: arrayRemove(targetUserId)
        });
    } catch (error) {
        console.error('Error unsharing toolkit:', error);
        throw error;
    }
};

export const createToolkitNotification = async (targetUserId: string, toolkitId: string, type: 'share' | 'publish'): Promise<void> => {
    try {
        const notificationRef = collection(db, 'notifications');
        await addDoc(notificationRef, {
            userId: targetUserId,
            type: `toolkit_${type}`,
            toolkitId,
            createdAt: new Date().toISOString(),
            read: false
        });
    } catch (error) {
        console.error('Error creating toolkit notification:', error);
        throw error;
    }
};

export const shareToolkitWithFollowers = async (toolkitId: string, followers: string[]): Promise<void> => {
    try {
        const docRef = doc(toolkitCollection, toolkitId);

        // Update toolkit with all followers
        await updateDoc(docRef, {
            shared_with: arrayUnion(...followers)
        });

        // Create notifications for all followers
        await Promise.all(followers.map((followerId: string) =>
            createToolkitNotification(followerId, toolkitId, 'share')
        ));
    } catch (error) {
        console.error('Error sharing toolkit with followers:', error);
        throw error;
    }
};

export const publishToPortfolio = async (toolkitId: string, userId: string): Promise<void> => {
    try {
        const toolkit = await getToolkit(toolkitId);
        if (!toolkit) throw new Error('Toolkit not found');

        const portfolioRef = collection(db, 'portfolio');
        const portfolioDoc = await addDoc(portfolioRef, {
            userId,
            type: 'toolkit',
            title: toolkit.name,
            description: toolkit.description,
            media_url: toolkit.logo_url || '',
            media_type: 'image',
            content_type: 'toolkit',
            tools: toolkit.tools,
            tags: toolkit.tags,
            likes: 0,
            views: 0,
            is_public: true,
            status: 'published',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            toolkitId
        });

        // Update the toolkit with the portfolio reference
        await updateDoc(doc(toolkitCollection, toolkitId), {
            portfolioId: portfolioDoc.id,
            is_published: true,
            published_at: new Date().toISOString()
        });

        // Create notification for followers
        const userRef = doc(collection(db, 'users'), userId);
        const userDoc = await getDoc(userRef);
        if (userDoc.exists()) {
            const followers: string[] = userDoc.data().followers || [];
            await Promise.all(followers.map((followerId: string) =>
                createToolkitNotification(followerId, toolkitId, 'publish')
            ));
        }
    } catch (error) {
        console.error('Error publishing toolkit to portfolio:', error);
        throw error;
    }
};
