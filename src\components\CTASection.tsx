import { But<PERSON> } from '@/components/ui/button';
import { ArrowR<PERSON> } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';

const CTASection = () => {
  return (
    <section className="py-16 md:py-24 px-4 bg-sortmy-darker">
      <div className="container mx-auto">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to build Your <span className="gradient-text">AI Brain</span>?
          </h2>
          <p className="text-gray-400 mb-8 text-lg">
            Track your tools. Build your AI brain. Showcase your genius.
          </p>

          <div className="flex justify-center">
            <Link to="/signup">
              <Button className="bg-sortmy-blue hover:bg-sortmy-blue/90 text-white px-8 py-6 rounded-md">
                Sign Up
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>


        </div>
      </div>
    </section>
  );
};

export default CTASection;
