import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { collection, addDoc, updateDoc, doc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Tool, Toolkit } from '@/types/tools';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { ImageUploader } from '@/components/ui/image-uploader';
import { ToolSelector } from './ToolSelector';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { storage } from '@/lib/firebase';

interface ToolkitManagerProps {
    isOpen: boolean;
    onClose: () => void;
    availableTools: Tool[];
    onSave: (toolkit: Toolkit) => void;
    editingToolkit?: Toolkit; // Change from Toolkit | null
}

export const ToolkitManager: React.FC<ToolkitManagerProps> = ({
    isOpen,
    onClose,
    availableTools,
    onSave,
    editingToolkit
}) => {
    const { user, isAdmin } = useAuth();
    const { toast } = useToast();
    const [name, setName] = useState(editingToolkit?.name || '');
    const [description, setDescription] = useState(editingToolkit?.description || '');
    const [logoUrl, setLogoUrl] = useState(editingToolkit?.logo_url || '');
    const [selectedTools, setSelectedTools] = useState<string[]>(editingToolkit?.tools || []);
    const [isSaving, setIsSaving] = useState(false);

    const handleImageUpload = async (file: File) => {
        if (!user) return null;
        try {
            const storageRef = ref(storage, `toolkit-logos/${user.uid}/${Date.now()}-${file.name}`);
            const result = await uploadBytes(storageRef, file);
            const url = await getDownloadURL(result.ref);
            setLogoUrl(url);
            return url;
        } catch (error) {
            console.error('Error uploading image:', error);
            toast({
                title: 'Error',
                description: 'Failed to upload image',
                variant: 'destructive'
            });
            return null;
        }
    };

    const handleSubmit = async () => {
        if (!user) return;

        try {
            setIsSaving(true);
            const toolkitData: Partial<Toolkit> = {
                name,
                description,
                logo_url: logoUrl,
                tools: selectedTools,
                created_by: user.uid,
                is_published: isAdmin ? true : false,
                source: isAdmin ? 'library' : 'user',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                tags: availableTools
                    .filter(tool => selectedTools.includes(tool.id))
                    .flatMap(tool => tool.tags || [])
                    .filter((tag, index, self) => self.indexOf(tag) === index)
            };

            if (editingToolkit) {
                // Update existing toolkit
                await updateDoc(doc(db, 'toolkits', editingToolkit.id), toolkitData);
            } else {
                // Create new toolkit
                const docRef = await addDoc(collection(db, 'toolkits'), toolkitData);
                toolkitData.id = docRef.id;
            }

            onSave(toolkitData as Toolkit);
            onClose();
            toast({
                title: `Toolkit ${editingToolkit ? 'updated' : 'created'}`,
                description: `The toolkit has been ${editingToolkit ? 'updated' : 'created'} successfully.`
            });
        } catch (error) {
            console.error('Error saving toolkit:', error);
            toast({
                title: 'Error',
                description: `Failed to ${editingToolkit ? 'update' : 'create'} toolkit.`,
                variant: 'destructive'
            });
        } finally {
            setIsSaving(false);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>{editingToolkit ? 'Edit' : 'Create'} Toolkit</DialogTitle>
                </DialogHeader>
                <div className="space-y-4 py-4">
                    <div className="space-y-2">
                        <Label htmlFor="name">Toolkit Name</Label>
                        <Input
                            id="name"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            placeholder="Enter toolkit name"
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="description">Description</Label>
                        <Textarea
                            id="description"
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            placeholder="Describe your toolkit..."
                        />
                    </div>

                    <ImageUploader
                        currentImage={logoUrl}
                        onUpload={handleImageUpload}
                        onImageSelected={setLogoUrl}
                        label="Upload Logo"
                    />

                    <div className="space-y-2">
                        <Label>Select Tools</Label>
                        <ToolSelector
                            availableTools={availableTools}
                            selectedTools={selectedTools}
                            onChange={setSelectedTools}
                        />
                    </div>

                    <div className="flex justify-end space-x-2">
                        <Button variant="outline" onClick={onClose}>
                            Cancel
                        </Button>
                        <Button
                            onClick={handleSubmit}
                            disabled={isSaving || !name || selectedTools.length === 0}
                        >
                            {isSaving ? 'Saving...' : editingToolkit ? 'Update' : 'Create'} Toolkit
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};
