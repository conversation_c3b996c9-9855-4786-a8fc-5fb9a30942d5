import { useState, useRef, useEffect } from 'react';
import { Trash2, ExternalLink, Plus, BookmarkIcon } from 'lucide-react';
import { type ImageSettings } from '@/types/tools';
import { Badge } from '@/components/ui/badge';
import { getStorage, ref, getDownloadURL } from 'firebase/storage';

export interface ToolIconProps {
  tool: {
    id: string;
    name: string;
    description?: string;
    logoUrl?: string;
    logo_url?: string;
    useCase?: string;
    createdBy?: string;
    createdAt?: string;
    updatedAt?: string;
    tags?: string[] | string;
    pricing?: string;
    website?: string;
    imageSettings?: ImageSettings;
  };
  isUserTool?: boolean;
  isLibraryTool?: boolean;
  onDelete?: () => void;  // Update these to not accept parameters
  onAdd?: () => void;
  onEdit?: () => void;
  isAdmin?: boolean;
  isSaving?: boolean;
}

export const ToolIcon: React.FC<ToolIconProps> = ({
  tool,
  isUserTool,
  isLibraryTool,
  onDelete,
  onAdd,
  onEdit,
  isAdmin,
  isSaving
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>('');
  const [imageError, setImageError] = useState(false);
  const tooltipTimeout = useRef<number>();
  const tooltipRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    return () => {
      if (tooltipTimeout.current) {
        clearTimeout(tooltipTimeout.current);
      }
    };
  }, []);

  const handleMouseEnter = () => {
    if (tooltipTimeout.current) {
      clearTimeout(tooltipTimeout.current);
    }
    setShowTooltip(true);
  };

  const handleMouseLeave = (e: React.MouseEvent) => {
    // Check if mouse is moving to tooltip
    const tooltipEl = tooltipRef.current;
    const containerEl = containerRef.current;

    if (tooltipEl && containerEl) {
      const tooltipBox = tooltipEl.getBoundingClientRect();
      const containerBox = containerEl.getBoundingClientRect();

      // If mouse is moving towards tooltip, don't hide
      if (
        e.clientX >= tooltipBox.left &&
        e.clientX <= tooltipBox.right &&
        e.clientY >= Math.min(containerBox.bottom, tooltipBox.top) &&
        e.clientY <= Math.max(containerBox.bottom, tooltipBox.top)
      ) {
        return;
      }
    }

    tooltipTimeout.current = window.setTimeout(() => {
      setShowTooltip(false);
    }, 100);
  };

  const handleVisit = () => {
    const url = tool.website ||
      '#';

    if (url !== '#') {
      // Format URL to ensure it has http/https prefix
      const formattedUrl = url.startsWith('http') ? url : `https://${url}`;
      window.open(formattedUrl, '_blank', 'noopener,noreferrer');
    }
  };

  // Move name declaration to the top
  const name = tool.name || '';

  // Load and validate image URL
  useEffect(() => {
    const loadImage = async () => {
      const url = tool.logoUrl || tool.logo_url;

      if (!url) {
        setImageUrl('');
        setImageError(true);
        return;
      }

      try {
        // Handle data and blob URLs directly
        if (url.startsWith('data:') || url.startsWith('blob:')) {
          setImageUrl(url);
          setImageError(false);
          return;
        }

        // Handle Firebase Storage URLs
        if (url.includes('firebasestorage.googleapis.com')) {
          const storage = getStorage();
          const urlPath = url.split('/o/')[1]?.split('?')[0];
          if (!urlPath) throw new Error('Invalid Firebase Storage URL');

          const decodedPath = decodeURIComponent(urlPath);
          const imageRef = ref(storage, decodedPath);
          const downloadUrl = await getDownloadURL(imageRef);
          setImageUrl(downloadUrl);
          setImageError(false);
          return;
        }

        // For external URLs, use proxy to avoid CORS
        const proxyUrl = `https://images.weserv.nl/?url=${encodeURIComponent(url)}`;
        const response = await fetch(proxyUrl);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

        const blob = await response.blob();
        const blobUrl = URL.createObjectURL(blob);
        setImageUrl(blobUrl);
        setImageError(false);
      } catch (error) {
        console.error('Error loading image:', error);
        setImageError(true);
        setImageUrl('');
      }
    };

    loadImage();

    return () => {
      // Cleanup blob URLs
      if (imageUrl?.startsWith('blob:')) {
        URL.revokeObjectURL(imageUrl);
      }
    };
  }, [tool]);

  // Get image settings if available (for AITool type)
  const imageSettings = tool.imageSettings;
  const objectFit = imageSettings?.size === 'contain' || imageSettings?.size === 'cover'
    ? imageSettings.size
    : imageSettings?.size ? undefined : 'contain';

  // Update tag handling
  const handleTags = (tags?: string | string[]): string[] => {
    if (!tags) return [];
    if (Array.isArray(tags)) return tags;
    return tags.split(',').map(t => t.trim()).filter(Boolean);
  };

  // Use the handleTags function
  const displayTags = handleTags(tool.tags);

  return (
    <div
      ref={containerRef}
      className="relative"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="flex flex-col items-center text-center">
        {/* App Icon */}
        <div
          className="w-16 h-16 rounded-2xl overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 mb-2 shadow-lg cursor-pointer relative"
        >
          {!imageError && imageUrl ? (
            <img
              src={imageUrl}
              alt={`${name} logo`}
              className="w-full h-full"
              crossOrigin="anonymous"
              loading="lazy"
              style={{
                objectFit,
                width: imageSettings?.size?.endsWith('%') ? imageSettings.size : '100%',
                height: imageSettings?.size?.endsWith('%') ? imageSettings.size : '100%',
                padding: imageSettings?.padding != null ? `${imageSettings.padding}px` : '0',
                objectPosition: imageSettings?.position || 'center'
              }}
              onError={(e) => {
                console.error('Image load error:', {
                  url: imageUrl,
                  error: e
                });
                setImageError(true);
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <span className="text-2xl font-bold text-white">{name.charAt(0)}</span>
            </div>
          )}
        </div>

        {/* App Name */}
        <span className="text-sm font-medium text-white truncate max-w-[80px]">
          {name}
        </span>
      </div>

      {/* Tooltip Card */}
      {showTooltip && (
        <div
          ref={tooltipRef}
          className="absolute z-50 w-64 p-4 bg-sortmy-darker/95 backdrop-blur-sm border border-[#01AAE9]/20 rounded-xl shadow-lg left-full ml-2 top-0"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={() => setShowTooltip(false)}
        >
          <div className="relative">
            {/* Arrow pointer - now points to the left */}
            <div className="absolute top-1/2 -left-4 -translate-y-1/2 border-8 border-transparent border-r-[#01AAE9]/20"></div>

            {/* Content */}
            <div className="space-y-3">
              <div className="flex items-start justify-between">
                <h3 className="font-semibold text-white">{name}</h3>
                {tool.pricing && (
                  <Badge
                    variant="outline"
                    className={`text-xs ${tool.pricing === 'Free'
                      ? 'bg-green-500/20 text-green-400 border-green-500/20'
                      : tool.pricing === 'Freemium'
                        ? 'bg-blue-500/20 text-blue-400 border-blue-500/30'
                        : 'bg-purple-500/20 text-purple-400 border-purple-500/30'
                      }`}
                  >
                    {tool.pricing}
                  </Badge>
                )}
              </div>

              {/* Description */}
              {(tool.description || tool.useCase) && (
                <p className="text-sm text-gray-300 line-clamp-2">
                  {tool.description || tool.useCase}
                </p>
              )}

              {/* Tags */}
              {tool.tags && tool.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {displayTags.slice(0, 3).map((tag: string, index: number) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="text-xs bg-[#01AAE9]/10 border-[#01AAE9]/20"
                    >
                      {tag}
                    </Badge>
                  ))}
                  {displayTags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{displayTags.length - 3}
                    </Badge>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col gap-2">
                {/* Visit Website Button */}
                <button
                  onClick={handleVisit}
                  className="w-full px-3 py-1.5 rounded-md bg-blue-500 hover:bg-blue-600 text-white flex items-center justify-center gap-2"
                >
                  <ExternalLink size={16} />
                  Visit Website
                </button>

                {/* Admin Edit Button */}
                {isAdmin && onEdit && (
                  <button
                    onClick={onEdit}  // Remove the tool argument
                    className="w-full px-3 py-1.5 rounded-md bg-green-500 hover:bg-green-600 text-white flex items-center justify-center gap-2"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                    </svg>
                    Edit Tool
                  </button>
                )}

                {/* Delete Button - for user tools or admin */}
                {(isUserTool || isAdmin) && (
                  <button
                    onClick={onDelete}  // Remove the tool argument
                    className="w-full px-3 py-1.5 rounded-md bg-red-500 hover:bg-red-600 text-white flex items-center justify-center gap-2"
                  >
                    <Trash2 size={16} />
                    Delete
                  </button>
                )}

                {/* Add Button */}
                {!isUserTool && (
                  <button
                    onClick={onAdd}  // Remove the tool argument
                    disabled={isSaving}
                    className={`w-full px-3 py-1.5 rounded-md flex items-center justify-center gap-2 ${isSaving
                      ? 'bg-blue-500/50 cursor-not-allowed'
                      : isLibraryTool
                        ? 'bg-emerald-600 hover:bg-emerald-700'
                        : 'bg-blue-500 hover:bg-blue-600'
                      } text-white transition-colors`}
                  >
                    {isSaving ? (
                      <>
                        <div className="w-4 h-4 rounded-full border-2 border-white border-t-transparent animate-spin"></div>
                        <span>Adding...</span>
                      </>
                    ) : (
                      <>
                        {isLibraryTool ? <BookmarkIcon size={16} /> : <Plus size={16} />}
                        <span>{isLibraryTool ? 'Add to Library' : 'Add Tool'}</span>
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
