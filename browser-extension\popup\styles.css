:root {
  --primary-color: #01AAE9;
  --dark-bg: #121212;
  --card-bg: #1e1e1e;
  --text-color: #ffffff;
  --border-color: rgba(1, 170, 233, 0.2);
}

body {
  width: 360px;
  height: 500px;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--dark-bg);
  color: var(--text-color);
  overflow-x: hidden;
}

.header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
}

.logo {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

h1 {
  font-size: 18px;
  margin: 0;
}

.hidden {
  display: none !important;
}

.primary-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.secondary-button {
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--border-color);
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
}

#auth-container {
  padding: 16px;
  display: flex;
  justify-content: center;
}

#user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

.tab-button {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--text-color);
  padding: 12px;
  cursor: pointer;
  opacity: 0.7;
}

.tab-button.active {
  opacity: 1;
  border-bottom: 2px solid var(--primary-color);
}

.tab-content {
  display: none;
  padding: 16px;
}

.tab-content.active {
  display: block;
}

.section-header {
  margin-bottom: 12px;
}

h2 {
  font-size: 16px;
  margin: 0;
}

.section-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin: 4px 0 12px 0;
}

.search-container {
  margin-bottom: 16px;
}

input {
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-color);
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  max-height: 350px;
  overflow-y: auto;
  padding: 8px;
}

.tool-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.tool-icon {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  background: linear-gradient(135deg, #2563eb, #7c3aed);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  position: relative;
}

.tool-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.tool-card:hover .tool-icon {
  transform: scale(1.05);
}

.tool-card:active .tool-icon {
  transform: scale(0.95);
}

.tool-actions {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: rgba(30, 30, 30, 0.9);
  border-radius: 50%;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
  border: 1px solid var(--border-color);
}

.tool-card:hover .tool-actions {
  opacity: 1;
}

.tool-name {
  font-size: 11px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 2px;
}

.loading {
  grid-column: span 4;
  text-align: center;
  padding: 20px;
  color: rgba(255, 255, 255, 0.6);
}

/* Custom tool icon colors based on first letter */
.tool-icon[data-first-letter="A"],
.tool-icon[data-first-letter="J"],
.tool-icon[data-first-letter="S"] {
  background: linear-gradient(135deg, #f97316, #ef4444);
}

.tool-icon[data-first-letter="B"],
.tool-icon[data-first-letter="K"],
.tool-icon[data-first-letter="T"] {
  background: linear-gradient(135deg, #06b6d4, #3b82f6);
}

.tool-icon[data-first-letter="C"],
.tool-icon[data-first-letter="L"],
.tool-icon[data-first-letter="U"] {
  background: linear-gradient(135deg, #8b5cf6, #d946ef);
}

.tool-icon[data-first-letter="D"],
.tool-icon[data-first-letter="M"],
.tool-icon[data-first-letter="V"] {
  background: linear-gradient(135deg, #10b981, #059669);
}

.tool-icon[data-first-letter="E"],
.tool-icon[data-first-letter="N"],
.tool-icon[data-first-letter="W"] {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.tool-icon[data-first-letter="F"],
.tool-icon[data-first-letter="O"],
.tool-icon[data-first-letter="X"] {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
}

.tool-icon[data-first-letter="G"],
.tool-icon[data-first-letter="P"],
.tool-icon[data-first-letter="Y"] {
  background: linear-gradient(135deg, #ec4899, #be185d);
}

.tool-icon[data-first-letter="H"],
.tool-icon[data-first-letter="Q"],
.tool-icon[data-first-letter="Z"] {
  background: linear-gradient(135deg, #14b8a6, #0d9488);
}

.tool-icon[data-first-letter="I"],
.tool-icon[data-first-letter="R"] {
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
}

/* External link icon styling */
.external-link-icon {
  color: var(--primary-color);
}