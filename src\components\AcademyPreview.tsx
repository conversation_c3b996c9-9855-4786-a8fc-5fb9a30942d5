import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Clock, Users } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON> } from "react-router-dom";

const AcademyPreview = () => {
  const courses = [
    {
      title: "Mastering Midjourney",
      description: "Turn ideas into stunning visuals using prompt engineering.",
      category: "Image Generation",
      level: "Intermediate",
      duration: "4 hours",
      students: 1245,
      rating: 4.8,
      image: "https://media.assettype.com/analyticsinsight%2F2024-07%2F6bb39b76-690b-4ee0-a41c-e6f284307002%2Ftop_5_midjourney_AI_alternatives.jpg"
    },
    {
      title: "ChatGPT for Business",
      description: "Automate emails, reports & client workflows in 30 mins a day.",
      category: "Productivity",
      level: "Beginner",
      duration: "3 hours",
      students: 2389,
      rating: 4.9,
      image: "https://blueerasoftech.com/wp-content/uploads/2023/03/Chat-GPT.jpg"
    },
    {
      title: "AI Video Production",
      description: "Create reels, ads, and faceless content at scale.",
      category: "Video Creation",
      level: "Advanced",
      duration: "6 hours",
      students: 876,
      rating: 4.7,
      image: "https://d3njjcbhbojbot.cloudfront.net/api/utilities/v1/imageproxy/https://images.ctfassets.net/wp1lcwdav1p1/56KACkKW05xh2bqHsLp4zk/e08b1ea5363f8fb9e850e93b13dd7163/GettyImages-1418210625.jpg?w=1500&h=680&q=60&fit=fill&f=faces&fm=jpg&fl=progressive&auto=format%2Ccompress&dpr=1&w=1000"
    }
  ];

  return (
    <section className="py-20 relative overflow-hidden bg-sortmy-darker">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto relative">
          {/* Background element */}
          <div className="absolute inset-0 flex items-center justify-center opacity-10 z-0 pointer-events-none">
            <BookOpen className="w-64 h-64 text-sortmy-blue" />
          </div>

          <div className="text-center mb-12 relative z-10">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">SortMyAI Academy</h2>
            <p className="text-xl text-gray-300 mb-8">
              Learn in demand AI tools and techniques

            </p>
          </div>

          {/* Course Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            {courses.map((course, index) => (
              <CourseCard key={index} course={course} />
            ))}
          </div>

          <div className="text-center relative z-10">

            <Link to="/academy">
              <Button className="bg-sortmy-blue hover:bg-sortmy-blue/90 text-white group relative overflow-hidden">
                <span className="relative z-10 flex items-center">
                  Explore Academy
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </span>
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

interface CourseCardProps {
  course: {
    title: string;
    description: string;
    category: string;
    level: string;
    duration: string;
    students: number;
    rating: number;
    image: string;
  };
}

const CourseCard = ({ course }: CourseCardProps) => {
  return (
    <Card className="overflow-hidden border-sortmy-gray/30 bg-sortmy-gray/10 hover:bg-sortmy-gray/20 transition-colors">
      <div className="relative h-48">
        <img
          src={course.image}
          alt={course.title}
          className="w-full h-full object-cover"
        />
        <Badge className="absolute top-3 right-3 bg-sortmy-blue text-white border-none">
          {course.category}
        </Badge>
      </div>

      <CardContent className="p-5">
        <h3 className="text-lg font-semibold mb-2">{course.title}</h3>
        <p className="text-sm text-gray-400 mb-3">{course.description}</p>

        <div className="flex items-center text-sm text-gray-400 mb-3">
          <Badge variant="outline" className="mr-2 border-sortmy-blue/30 text-sortmy-blue">
            {course.level}
          </Badge>
          <div className="flex items-center mr-3">
            <Clock className="w-4 h-4 mr-1 text-sortmy-blue" />
            {course.duration}
          </div>
          <div className="flex items-center">
            <Users className="w-4 h-4 mr-1 text-sortmy-blue" />
            {course.students.toLocaleString()}
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Star className="w-4 h-4 text-yellow-400 mr-1" />
            <span className="font-medium">{course.rating}</span>
          </div>
          <Link to={`/academy/course/${course.title.toLowerCase().replace(/\s+/g, '-')}`}>
            <Button variant="outline" size="sm" className="border-sortmy-blue/30 text-sortmy-blue hover:bg-sortmy-blue/10">
              View Course
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
};

export default AcademyPreview;
