import { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Link } from 'react-router-dom';
import { collection, query, where, getDocs, deleteDoc, doc, getDoc, updateDoc, arrayRemove, arrayUnion, writeBatch } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useToast } from '@/hooks/use-toast';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { AlertDialog, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { PlusCircle, Search, Briefcase, Loader2, X, Hash, Tag, FileText, Info, Bookmark } from 'lucide-react';
import GlassCard from '@/components/ui/GlassCard';
import NeonButton from '@/components/ui/NeonButton';
import ClickEffect from '@/components/ui/ClickEffect';
import { Separator } from '@/components/ui/separator';
import { Tool, AITool, ImageSettings, ImageSizeType, ImagePositionType } from '@/types';
import ToolIcon from '@/components/tools/ToolIcon';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import ImageCropper from '@/components/ui/ImageCropper';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { storage } from '@/lib/firebase';

// Helper function to format website URLs
const formatWebsiteUrl = (url: string) => {
  if (!url) return '';
  if (!/^https?:\/\//i.test(url)) {
    return `https://${url}`;
  }
  return url;
};

// Add this utility function at the top level
const getSecureStorageUrl = (url: string | undefined): string => {
  if (!url) return '';
  if (url.startsWith('data:')) return url;

  // Handle Firebase Storage URLs
  if (url.includes('firebasestorage.googleapis.com')) {
    try {
      // Extract bucket and path
      const match = url.match(/firebasestorage\.googleapis\.com\/v0\/b\/([^/]+)\/o\/([^?]+)/);
      if (match) {
        const [, bucket, path] = match;
        // Convert to direct storage URL
        return `https://storage.googleapis.com/${bucket}/${decodeURIComponent(path)}`;
      }
    } catch (error) {
      console.error('Error parsing Firebase Storage URL:', error);
    }
  }

  return url;
};

// Define the UserTool interface that combines Tool and AITool properties
interface UserTool {
  id: string;
  name: string;
  description?: string;
  logo_url?: string;
  logoUrl?: string;
  website_url?: string;
  website?: string;
  tags?: string[];
  category?: string;
  created_at?: string;
  updated_at?: string;
  user_id?: string;
  source: 'tools_collection' | 'user_tooltracker' | 'library';
  // AITool specific properties
  useCase?: string;
  pricing?: 'Free' | 'Freemium' | 'Paid' | 'Subscription';
  excelsAt?: string;
  websiteLink?: string;
  logoLink?: string;
  imageSettings?: ImageSettings;
}

// Validate image size function
const validateImageSize = (size?: string): ImageSizeType => {
  if (size === 'contain' || size === 'cover' || size === '85%' || size === '75%' || size === '50%') {
    return size as ImageSizeType;
  }
  return 'contain';
};

const CombinedToolTracker = () => {
  const { user, isAdmin } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const libraryRef = useRef<HTMLDivElement>(null);

  // Common state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [allTags, setAllTags] = useState<string[]>([]);

  // Search suggestions state
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Define types for search suggestions
  type SuggestionType = 'name' | 'description' | 'category' | 'tag';

  interface SearchSuggestion {
    text: string;
    type: SuggestionType;
    source: 'user' | 'library' | 'both';
    toolId?: string;
  }

  // User tools state
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [toolToDelete, setToolToDelete] = useState<Tool | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  // Library tools state
  const [savingToolIds, setSavingToolIds] = useState<string[]>([]);
  const [editingTool, setEditingTool] = useState<AITool | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isEditingImage, setIsEditingImage] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Update the userTools query with error handling
  const { data: userTools, isLoading: isLoadingUserTools, error: userToolsError } = useQuery<UserTool[]>({
    queryKey: ['userTools', user?.uid],
    queryFn: async () => {
      if (!user?.uid) return [];

      try {
        // Fetch from tools collection
        const toolsRef = collection(db, 'tools');
        const q = query(toolsRef, where('user_id', '==', user?.uid));
        const snapshot = await getDocs(q); const toolsFromCollection = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          source: 'tools_collection' as const
        })) as UserTool[];

        // Fetch from user's toolTracker array
        const userRef = doc(db, 'users', user.uid);
        const userDoc = await getDoc(userRef);

        if (!userDoc.exists()) return toolsFromCollection;

        const userData = userDoc.data();
        const toolTracker = userData.toolTracker || [];

        // Convert toolTracker items to Tool format
        const toolsFromTracker = toolTracker.map((tool: any) => {
          // Extract tags from the tool if available
          let tags: string[] = [];
          if (tool.tags) {
            tags = Array.isArray(tool.tags) ? tool.tags :
              typeof tool.tags === 'string' ? tool.tags.split(',').map((t: string) => t.trim()) : [];
          }

          return {
            id: tool.id,
            name: tool.name,
            description: tool.useCase || tool.description || '',
            logo_url: tool.logoUrl || tool.logoLink,
            website_url: tool.website || tool.websiteLink,
            website: tool.website || tool.websiteLink,
            tags: tags,
            created_at: tool.addedAt || new Date().toISOString(),
            user_id: user.uid,
            category: tool.category || '',
            price_tier: tool.pricing || 'free',
            notes: tool.excelsAt || '',
            source: 'user_tooltracker'
          };
        });

        return [...toolsFromCollection, ...toolsFromTracker] as (Tool & { source: string })[];
      } catch (error) {
        console.error('Error fetching user tools:', error);
        throw error;
      }
    },
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    enabled: !!user?.uid,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes (formerly cacheTime)
  });

  // Update the library tools query with error handling
  const { data: libraryTools, isLoading: isLoadingLibrary } = useQuery<AITool[]>({
    queryKey: ['aiTools'],
    queryFn: async () => {
      try {
        const toolsCollection = collection(db, 'aiTools');
        const toolsSnapshot = await getDocs(toolsCollection);

        const toolsList: AITool[] = [];
        toolsSnapshot.forEach((doc) => {
          const toolData = doc.data() as AITool;
          const tool = {
            ...toolData,
            id: doc.id
          };

          // Convert tags to array if it's a string
          if (tool.tags) {
            if (typeof tool.tags === 'string') {
              tool.tags = tool.tags.split(',').map(tag => tag.trim());
            }
          }

          toolsList.push(tool);
        });

        return toolsList;
      } catch (error) {
        console.error('Error fetching library tools:', error);
        throw error;
      }
    },
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  });

  // Loading and error states
  const isLoading = isLoadingUserTools || isLoadingLibrary;
  const error = userToolsError;

  // Extract all unique tags from both user tools and library tools
  useEffect(() => {
    const tagsSet = new Set<string>();

    // Add tags from user tools
    if (userTools && Array.isArray(userTools)) {
      userTools.forEach((tool: UserTool) => {
        if (tool.tags && Array.isArray(tool.tags)) {
          tool.tags.forEach((tag: string) => tagsSet.add(tag));
        }
      });
    }

    // Add tags from library tools
    if (libraryTools && Array.isArray(libraryTools)) {
      libraryTools.forEach((tool: AITool) => {
        if (tool.tags && Array.isArray(tool.tags)) {
          tool.tags.forEach((tag: string) => tagsSet.add(tag));
        }
      });
    }

    setAllTags(Array.from(tagsSet));
  }, [userTools, libraryTools]);

  // Generate search suggestions based on the search query
  const generateSuggestions = (query: string) => {
    if (!query || query.length < 2) {
      setSuggestions([]);
      return;
    }

    const queryLower = query.toLowerCase();
    const newSuggestions: SearchSuggestion[] = [];
    const addedSuggestions = new Set<string>(); // To avoid duplicates

    // Helper function to add a suggestion if it's not already added
    const addSuggestion = (text: string, type: SuggestionType, source: 'user' | 'library' | 'both', toolId?: string) => {
      const key = `${text}-${type}`;
      if (!addedSuggestions.has(key) && text.toLowerCase().includes(queryLower)) {
        newSuggestions.push({ text, type, source, toolId });
        addedSuggestions.add(key);
      }
    };

    // Add suggestions from user tools
    if (userTools && userTools.length > 0) {
      userTools.forEach(tool => {
        // Add name suggestions
        if ((tool as any).name) {
          addSuggestion((tool as any).name, 'name', 'user', tool.id);
        }

        // Add description suggestions
        if ((tool as any).description) {
          const desc = (tool as any).description;
          // Only add if the description is not too long and contains the query
          if (desc.length < 50) {
            addSuggestion(desc, 'description', 'user', tool.id);
          }
        }

        // Add category suggestions
        if ((tool as any).category) {
          addSuggestion((tool as any).category, 'category', 'user', tool.id);
        }

        // Add tag suggestions
        if ((tool as any).tags && Array.isArray((tool as any).tags)) {
          (tool as any).tags.forEach((tag: string) => {
            addSuggestion(tag, 'tag', 'user', tool.id);
          });
        }
      });
    }

    // Add suggestions from library tools
    if (libraryTools && libraryTools.length > 0) {
      libraryTools.forEach(tool => {
        // Add name suggestions
        addSuggestion(tool.name, 'name', 'library', tool.id);

        // Add description/useCase suggestions
        if (tool.description && tool.description.length < 50) {
          addSuggestion(tool.description, 'description', 'library', tool.id);
        }
        if (tool.useCase && tool.useCase.length < 50) {
          addSuggestion(tool.useCase, 'description', 'library', tool.id);
        }

        // Add tag suggestions
        if (tool.tags) {
          const tags = Array.isArray(tool.tags)
            ? tool.tags
            : typeof tool.tags === 'string'
              ? tool.tags.split(',').map(t => t.trim())
              : [];

          tags.forEach(tag => {
            // Check if this tag also exists in user tools
            const existsInUserTools = userTools?.some(userTool =>
              (userTool as any).tags &&
              Array.isArray((userTool as any).tags) &&
              (userTool as any).tags.includes(tag)
            );

            addSuggestion(tag, 'tag', existsInUserTools ? 'both' : 'library', tool.id);
          });
        }
      });
    }

    // Limit the number of suggestions to avoid overwhelming the UI
    setSuggestions(newSuggestions.slice(0, 10));
  };

  // Update suggestions when search query changes
  useEffect(() => {
    generateSuggestions(searchQuery);
  }, [searchQuery, userTools, libraryTools]);

  // Handle clicks outside the suggestions dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        showSuggestions &&
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target as Node) &&
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSuggestions]);

  // Filter user tools
  const filteredUserTools = userTools && Array.isArray(userTools) ? userTools.filter((tool: UserTool) => {
    // Filter by search query
    const searchLower = searchQuery.toLowerCase();
    const matchesSearch = (
      (tool as any).name?.toLowerCase().includes(searchLower) ||
      (tool as any).description?.toLowerCase().includes(searchLower) ||
      ((tool as any).tags && Array.isArray((tool as any).tags) && (tool as any).tags.some((tag: string) =>
        typeof tag === 'string' && tag.toLowerCase().includes(searchLower)
      ))
    );

    // Filter by selected tags
    const matchesTags = selectedTags.length === 0 || (
      (tool as any).tags &&
      Array.isArray((tool as any).tags) &&
      selectedTags.every(tag => (tool as any).tags.includes(tag))
    );

    return matchesSearch && matchesTags;
  }) : [];

  // Filter library tools
  const filteredLibraryTools = libraryTools && Array.isArray(libraryTools) ? libraryTools.filter((tool: AITool) => {
    // Filter by search query
    const searchLower = searchQuery.toLowerCase();
    const matchesSearch = (
      tool.name.toLowerCase().includes(searchLower) ||
      (tool.description && tool.description.toLowerCase().includes(searchLower)) ||
      (tool.useCase && tool.useCase.toLowerCase().includes(searchLower)) ||
      (tool.tags && Array.isArray(tool.tags) && tool.tags.some(tag =>
        typeof tag === 'string' && tag.toLowerCase().includes(searchLower)
      ))
    );

    // Filter by selected tags
    const matchesTags = selectedTags.length === 0 || (
      tool.tags &&
      Array.isArray(tool.tags) &&
      selectedTags.every(tag => tool.tags.includes(tag))
    );

    return matchesSearch && matchesTags;
  }) : [];

  // Toggle tag selection
  const toggleTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  // Handle selecting a suggestion
  const handleSelectSuggestion = (suggestion: SearchSuggestion) => {
    // If it's a tag, add it to selected tags
    if (suggestion.type === 'tag') {
      if (!selectedTags.includes(suggestion.text)) {
        toggleTag(suggestion.text);
      }
      // Clear the search query if it was used to find this tag
      if (searchQuery.toLowerCase().includes(suggestion.text.toLowerCase())) {
        setSearchQuery('');
      }
    } else {
      // For other types, set the search query to the suggestion text
      setSearchQuery(suggestion.text);
    }

    // Hide suggestions after selection
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);

    // Focus back on the input
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Handle keyboard navigation for suggestions
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev =>
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : 0);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0 && selectedSuggestionIndex < suggestions.length) {
          handleSelectSuggestion(suggestions[selectedSuggestionIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
        break;
      default:
        break;
    }
  };
  // Handle deleting a user tool
  const handleDeleteClick = (tool: any) => {
    // Check if trying to delete a library tool without admin privileges
    if ((tool as any).source === 'library' && !isAdmin) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can delete library tools.',
        variant: 'destructive',
      });
      return;
    }
    setToolToDelete(tool as Tool);
    setShowDeleteDialog(true);
  };

  // Confirm deletion of a user tool
  const confirmDelete = async () => {
    if (!toolToDelete) return;

    setIsDeleting(true);
    try {
      // Check the source of the tool to determine how to delete it
      if ((toolToDelete as any).source === 'tools_collection') {
        // Delete from tools collection
        await deleteDoc(doc(db, 'tools', toolToDelete.id));
      } else if ((toolToDelete as any).source === 'user_tooltracker') {
        // Delete from user's toolTracker array
        const userRef = doc(db, 'users', user!.uid);

        // Get the current toolTracker array
        const userDoc = await getDoc(userRef);
        if (userDoc.exists()) {
          const userData = userDoc.data();
          const toolTracker = userData.toolTracker || [];

          // Find the tool to remove
          const toolToRemove = toolTracker.find((tool: any) => tool.id === toolToDelete.id);

          if (toolToRemove) {
            // Remove the tool from the array
            await updateDoc(userRef, {
              toolTracker: arrayRemove(toolToRemove)
            });
          }
        }
      }

      // Invalidate and refetch the userTools query
      await queryClient.invalidateQueries({ queryKey: ['userTools', user?.uid] });

      toast({
        title: "Tool deleted",
        description: "Your tool has been removed successfully.",
      });
    } catch (error) {
      console.error('Error deleting tool:', error);
      toast({
        title: "Error",
        description: "Failed to delete the tool. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
      setToolToDelete(null);
    }
  };

  // Add a library tool to user's tools
  const addToolToTracker = async (tool: AITool) => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please sign in to add tools to your tracker.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSavingToolIds(prev => [...prev, tool.id]);

      // Process tags if they exist
      let tags: string[] = [];
      if (tool.tags) {
        tags = Array.isArray(tool.tags) ? tool.tags :
          typeof tool.tags === 'string' ? tool.tags.split(',').map(tag => tag.trim()) : [];
      }

      // Add tool to user's tool tracker in Firebase
      const userRef = doc(db, 'users', user.uid);
      await updateDoc(userRef, {
        toolTracker: arrayUnion({
          id: tool.id,
          name: tool.name,
          useCase: tool.useCase,
          description: tool.description,
          logoUrl: tool.logoUrl || tool.logoLink,
          website: formatWebsiteUrl(tool.website || tool.websiteLink || ''),
          tags: tags,
          pricing: tool.pricing,
          excelsAt: tool.excelsAt,
          addedAt: new Date().toISOString(), imageSettings: tool.imageSettings || {
            size: 'contain',
            position: 'center',
            padding: 0,
            scale: 1,
            rotate: 0
          }
        })
      });

      // Invalidate and refetch the userTools query
      await queryClient.invalidateQueries({ queryKey: ['userTools', user?.uid] });

      toast({
        title: 'Success',
        description: `${tool.name} has been added to your tool tracker.`,
        variant: 'default',
      });
    } catch (error) {
      console.error('Error adding tool to tracker:', error);
      toast({
        title: 'Error',
        description: 'Failed to add tool to your tracker. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSavingToolIds(prev => prev.filter(id => id !== tool.id));
    }
  };

  // Handle editing a tool (admin only)
  const handleEditTool = async (tool: AITool) => {
    if (!isAdmin) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can edit tools.',
        variant: 'destructive',
      });
      return;
    }
    setEditingTool(tool);
    setIsEditing(true);
  };

  // Handle saving edited tool
  const handleSaveEdit = async (updatedTool: AITool) => {
    if (!editingTool || !isAdmin) return;

    try {
      // Clean and secure the URL, keeping original Firebase Storage URL
      const secureLogoUrl = updatedTool.logoUrl;

      // Update tool in Firebase
      const toolRef = doc(db, 'aiTools', editingTool.id);
      await updateDoc(toolRef, {
        name: updatedTool.name,
        description: updatedTool.description || null,
        logoUrl: secureLogoUrl,
        website: updatedTool.website || null,
        tags: Array.isArray(updatedTool.tags) ? updatedTool.tags :
          updatedTool.tags?.split(',').map(tag => tag.trim()).filter(Boolean) || [],
        imageSettings: {
          size: (updatedTool.imageSettings?.size as ImageSizeType) || 'contain',
          position: (updatedTool.imageSettings?.position as ImagePositionType) || 'center',
          padding: Number(updatedTool.imageSettings?.padding || 0),
          scale: updatedTool.imageSettings?.scale || 1,
          rotate: updatedTool.imageSettings?.rotate || 0
        },
        updatedAt: new Date().toISOString()
      });

      // Update user's tool tracker if this tool is in any user's tracker
      const usersRef = collection(db, 'users');
      const usersWithTool = await getDocs(
        query(usersRef, where('toolTracker', 'array-contains', { id: editingTool.id }))
      );

      // Batch update all users who have this tool
      if (!usersWithTool.empty) {
        const batch = writeBatch(db);
        usersWithTool.docs.forEach(userDoc => {
          const userData = userDoc.data();
          const toolTracker = userData.toolTracker || [];
          const updatedToolTracker = toolTracker.map((tool: AITool) => {
            if (tool.id === editingTool.id) {
              return {
                ...tool,
                name: updatedTool.name,
                description: updatedTool.description,
                logoUrl: updatedTool.logoUrl,
                website: updatedTool.website,
                imageSettings: {
                  size: (updatedTool.imageSettings?.size as ImageSizeType) || 'contain',
                  position: (updatedTool.imageSettings?.position as ImagePositionType) || 'center',
                  padding: Number(updatedTool.imageSettings?.padding || 0),
                  scale: updatedTool.imageSettings?.scale || 1,
                  rotate: updatedTool.imageSettings?.rotate || 0
                }
              };
            }
            return tool;
          });
          batch.update(userDoc.ref, { toolTracker: updatedToolTracker });
        });
        await batch.commit();
      }

      // Refresh tool data
      await queryClient.invalidateQueries({ queryKey: ['aiTools'] });
      await queryClient.invalidateQueries({ queryKey: ['userTools'] });

      toast({
        title: 'Success',
        description: 'Tool has been updated successfully.',
        variant: 'default',
      });

      setIsEditing(false);
      setEditingTool(null);
    } catch (error) {
      console.error('Error updating tool:', error);
      toast({
        title: 'Error',
        description: 'Failed to update tool. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleImageUpload = async (file: File, toolId: string) => {
    try {
      setIsUploading(true);

      // Create storage reference with metadata
      const storageRef = ref(storage, `tool-logos/${toolId}/${file.name}`);

      // Set proper metadata for CORS
      const metadata = {
        contentType: file.type,
        cacheControl: 'public,max-age=31536000',
        customMetadata: {
          'Access-Control-Allow-Origin': '*'
        }
      };

      // Upload with metadata
      await uploadBytes(storageRef, file, metadata);

      // Get the download URL
      const downloadURL = await getDownloadURL(storageRef);

      // Update the editing tool with the new URL
      if (editingTool) {
        setEditingTool({
          ...editingTool,
          logoUrl: downloadURL
        });
      }

      toast({
        title: 'Success',
        description: 'Image uploaded successfully',
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: 'Error',
        description: 'Failed to upload image. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  const scrollToLibrary = () => {
    libraryRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Type for object-fit in image style
  const getObjectFit = (size?: string): 'contain' | 'cover' => {
    if (size === 'contain' || size === 'cover') return size;
    return 'contain';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Tool Tracker</h2>
        </div>        <div className="flex gap-3">          {isAdmin && (
          <ClickEffect effect="ripple" color="blue">
            <Link to="/dashboard/tools/library/add">
              <NeonButton variant="magenta">
                <PlusCircle className="w-4 h-4 mr-2" />
                Add to Library
              </NeonButton>
            </Link>
          </ClickEffect>
        )}          <ClickEffect effect="ripple" color="blue">
            <Link to="/dashboard/tools/add">
              <NeonButton variant="gradient">
                <PlusCircle className="w-4 h-4 mr-2" />
                Add Tool
              </NeonButton>
            </Link>
          </ClickEffect>
        </div>
      </div>

      {/* Search */}
      <div className="relative w-full">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4 z-10" />
        <Input
          ref={searchInputRef}
          placeholder="Search tools by name, description, or tag..."
          className="pl-10 bg-sortmy-darker/50 border-[#01AAE9]/20 focus:border-[#01AAE9]/40 focus:ring-[#01AAE9]/10"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onFocus={() => setShowSuggestions(true)}
          onKeyDown={handleKeyDown}
        />

        {/* Suggestions Dropdown */}
        {showSuggestions && searchQuery.length >= 2 && (
          <div
            ref={suggestionsRef}
            className="absolute z-50 w-full mt-1 bg-sortmy-darker border border-[#01AAE9]/20 rounded-md shadow-lg max-h-60 overflow-auto"
            style={{ boxShadow: '0 4px 20px rgba(1, 170, 233, 0.2)' }}
          >
            <div className="p-2">
              {suggestions.length > 0 ? (
                suggestions.map((suggestion, index) => (
                  <div
                    key={`${suggestion.text}-${suggestion.type}-${index}`}
                    className={`
                      flex items-center p-2 rounded-md cursor-pointer
                      ${selectedSuggestionIndex === index ? 'bg-[#01AAE9]/20' : 'hover:bg-[#01AAE9]/10'}
                    `}
                    onClick={() => handleSelectSuggestion(suggestion)}
                  >
                    {/* Icon based on suggestion type */}
                    {suggestion.type === 'name' && <Search className="h-4 w-4 mr-2 text-[#01AAE9]" />}
                    {suggestion.type === 'description' && <FileText className="h-4 w-4 mr-2 text-[#01AAE9]" />}
                    {suggestion.type === 'category' && <Bookmark className="h-4 w-4 mr-2 text-[#01AAE9]" />}
                    {suggestion.type === 'tag' && <Hash className="h-4 w-4 mr-2 text-[#01AAE9]" />}

                    <div className="flex-1">
                      <span className="text-sm text-white">{suggestion.text}</span>
                      <span className="ml-2 text-xs text-gray-400">
                        {suggestion.type === 'name' ? 'Tool' :
                          suggestion.type === 'description' ? 'Description' :
                            suggestion.type === 'category' ? 'Category' : 'Tag'}
                      </span>
                    </div>

                    {/* Source indicator */}
                    <Badge
                      variant="outline"
                      className={`
                        text-xs
                        ${suggestion.source === 'user' ? 'bg-green-500/10 text-green-400 border-green-500/20' :
                          suggestion.source === 'library' ? 'bg-blue-500/10 text-blue-400 border-blue-500/20' :
                            'bg-purple-500/10 text-purple-400 border-purple-500/20'}
                      `}
                    >
                      {suggestion.source === 'user' ? 'Your Tool' :
                        suggestion.source === 'library' ? 'Library' : 'Both'}
                    </Badge>
                  </div>
                ))
              ) : (
                <div className="p-3 text-center text-gray-400">
                  <Info className="h-4 w-4 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No suggestions found for "{searchQuery}"</p>
                  <p className="text-xs mt-1">Try a different search term or browse by tags</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Tags Filter - Limit to 8 tags */}
      <div className="flex flex-wrap gap-2">
        <div className="flex items-center mr-2">
          <Tag className="h-4 w-4 text-[#01AAE9] mr-1" />
          <span className="text-sm font-medium text-white">Tags:</span>
        </div>
        {allTags.slice(0, 8).map(tag => (
          <Badge
            key={tag}
            variant={selectedTags.includes(tag) ? "default" : "outline"}
            className={`cursor-pointer ${selectedTags.includes(tag)
              ? "bg-[#01AAE9] text-white hover:bg-[#01AAE9]/80"
              : "hover:bg-[#01AAE9]/10 border-[#01AAE9]/20"
              }`}
            onClick={() => toggleTag(tag)}
          >
            {tag}
          </Badge>
        ))}
        {selectedTags.length > 0 && (
          <Badge
            variant="outline"
            className="cursor-pointer hover:bg-red-500/10 border-red-500/20 text-red-400"
            onClick={() => setSelectedTags([])}
          >
            Clear Filters
            <X className="ml-1 h-3 w-3" />
          </Badge>
        )}
      </div>

      {/* User's Tools Section */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold text-white">Your Tools</h3>

        {isLoading ? (
          <div className="p-4 bg-sortmy-darker/40 border border-[#01AAE9]/20 rounded-xl">
            <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 gap-6 justify-items-center">
              {Array.from({ length: 12 }).map((_, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div className="w-16 h-16 rounded-2xl bg-[#01AAE9]/20 mb-2 animate-pulse"></div>
                  <div className="w-16 h-3 bg-[#01AAE9]/20 rounded mb-1 animate-pulse"></div>
                  <div className="w-12 h-3 bg-[#01AAE9]/10 rounded animate-pulse"></div>
                </div>
              ))}
            </div>
          </div>
        ) : error ? (
          <div className="bg-sortmy-darker border border-red-500/20 rounded-lg p-6 text-center">
            <p className="text-red-400">Error loading your tools. Please try again later.</p>
          </div>
        ) : filteredUserTools && filteredUserTools.length > 0 ? (
          <div className="p-4 bg-sortmy-darker/40 border border-[#01AAE9]/20 rounded-xl">
            <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 gap-6 justify-items-center">              {filteredUserTools.map(tool => {
              // Convert the tool object to match the Tool interface
              const toolProps: Tool = {
                id: tool.id,
                name: tool.name || '',
                description: tool.description || '',
                logo_url: tool.logo_url || tool.logoUrl || tool.logoLink || '',
                website_url: tool.website_url || tool.website || tool.websiteLink || '',
                tags: tool.tags || [],
                created_at: tool.created_at || new Date().toISOString(),
                source: tool.source || 'user_tooltracker'
              };

              return (
                <div key={tool.id} className="tool-icon-container">
                  <ToolIcon
                    tool={toolProps}
                    isUserTool={true}
                    isLibraryTool={toolProps.source === 'library'}
                    onDelete={() => handleDeleteClick(tool)}
                  />
                </div>
              );
            })}
            </div>
          </div>
        ) : (
          <div className="bg-sortmy-darker border border-[#01AAE9]/20 rounded-lg p-6 text-center">
            <Briefcase className="mx-auto w-12 h-12 mb-3 opacity-30" />
            <p className="text-gray-400">You haven't added any tools yet</p>
            <div className="flex justify-center mt-4">
              <button
                onClick={scrollToLibrary}
                className="px-4 py-2 bg-[#01AAE9] text-white rounded-md flex items-center hover:bg-[#01AAE9]/90 transition-colors"
              >
                <PlusCircle className="w-4 h-4 mr-2" />
                Browse Library
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Separator */}
      <Separator className="bg-sortmy-blue/20 my-8" />

      {/* AI Tools Library Section - Add ref here */}
      <div ref={libraryRef} className="space-y-4">
        <h3 className="text-xl font-semibold text-white">AI Tools Library</h3>
        <p className="text-gray-400">Discover and add new AI tools to your collection</p>

        {isLoadingLibrary ? (
          <div className="p-4 bg-sortmy-darker/40 border border-sortmy-blue/20 rounded-xl">
            <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 gap-6 justify-items-center">
              {Array.from({ length: 12 }).map((_, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div className="w-16 h-16 rounded-2xl bg-sortmy-blue/20 mb-2 animate-pulse"></div>
                  <div className="w-16 h-3 bg-sortmy-blue/20 rounded mb-1 animate-pulse"></div>
                  <div className="w-12 h-3 bg-sortmy-blue/10 rounded animate-pulse"></div>
                </div>
              ))}
            </div>
          </div>
        ) : filteredLibraryTools && filteredLibraryTools.length > 0 ? (
          <div className="p-4 bg-sortmy-darker/40 border border-sortmy-blue/20 rounded-xl">
            <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 gap-6 justify-items-center">
              {filteredLibraryTools.map(tool => (
                <div key={tool.id} className="tool-icon-container">
                  <ToolIcon
                    tool={tool}
                    isUserTool={false}
                    onAdd={() => addToolToTracker(tool)}
                    onEdit={() => handleEditTool(tool)}
                    onDelete={() => handleDeleteClick({ ...tool, source: 'library' })}
                    isAdmin={isAdmin}
                    isSaving={savingToolIds.includes(tool.id)}
                  />
                </div>
              ))}
            </div>
          </div>
        ) : (
          <GlassCard variant="bordered" className="border-sortmy-blue/20">
            <CardContent className="p-6 text-center">
              <p className="text-gray-400">No AI tools found matching your search criteria.</p>
            </CardContent>
          </GlassCard>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent className="bg-sortmy-dark border-[#01AAE9]/20 backdrop-blur-md">
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Tool?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{(toolToDelete as any)?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <button
              className="px-4 py-2 rounded-md bg-transparent border border-[#01AAE9]/20 text-white hover:bg-[#01AAE9]/10 transition-colors"
              disabled={isDeleting}
              onClick={() => setShowDeleteDialog(false)}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 rounded-md bg-red-500/80 text-white hover:bg-red-500 transition-colors"
              onClick={confirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 inline animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit Tool Dialog */}
      <Dialog open={isEditing} onOpenChange={(open) => !open && setIsEditing(false)}>
        <DialogContent className="bg-sortmy-dark border-[#01AAE9]/20 backdrop-blur-md max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Tool</DialogTitle>
            <DialogDescription>
              Update the tool details below.
            </DialogDescription>
          </DialogHeader>

          {editingTool && (
            <div className="space-y-6 py-4">
              {/* Name and Description */}
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={editingTool.name}
                    onChange={(e) => {
                      setEditingTool({ ...editingTool, name: e.target.value });
                    }}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={editingTool.description || ''}
                    onChange={(e) => {
                      setEditingTool({ ...editingTool, description: e.target.value });
                    }}
                    className="min-h-[100px]"
                  />
                </div>
              </div>

              {/* Image Section */}
              <div className="space-y-4">
                <Label>Tool Logo</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Image Preview */}
                  <div className="space-y-4">
                    <div className="relative w-40 h-40 mx-auto rounded-2xl overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg">
                      {editingTool.logoUrl ? (
                        <img
                          src={getSecureStorageUrl(editingTool.logoUrl)}
                          alt={`${editingTool.name} logo`}
                          className="w-full h-full"
                          crossOrigin="anonymous"
                          style={{
                            objectFit: getObjectFit(editingTool.imageSettings?.size),
                            padding: `${editingTool.imageSettings?.padding || 0}px`,
                            objectPosition: editingTool.imageSettings?.position || 'center',
                            transform: `scale(${editingTool.imageSettings?.scale || 1}) rotate(${editingTool.imageSettings?.rotate || 0}deg)`
                          }}
                          onError={(e) => {
                            const imgElement = e.currentTarget;
                            if (imgElement.src.includes('?')) {
                              // Try without parameters
                              imgElement.src = imgElement.src.split('?')[0];
                            } else {
                              // Show fallback
                              imgElement.style.display = 'none';
                              const parent = imgElement.parentElement;
                              if (parent) {
                                parent.classList.add('flex', 'items-center', 'justify-center');
                                const fallback = document.createElement('span');
                                fallback.className = 'text-4xl font-bold text-white';
                                fallback.textContent = editingTool.name.charAt(0);
                                parent.appendChild(fallback);
                              }
                            }
                          }}
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <span className="text-4xl font-bold text-white">{editingTool.name.charAt(0)}</span>
                        </div>
                      )}
                    </div>

                    {/* Upload Controls */}
                    <div className="flex flex-col gap-2">
                      <Label
                        htmlFor="logo-upload"
                        className="flex flex-col items-center justify-center p-4 border-2 border-dashed border-[#01AAE9]/40 rounded-xl cursor-pointer hover:border-[#01AAE9]/60 transition-colors"
                      >
                        <input
                          id="logo-upload"
                          type="file"
                          className="hidden"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file && editingTool) {
                              handleImageUpload(file, editingTool.id);
                            }
                          }}
                          disabled={isUploading}
                        />
                        <div className="flex flex-col items-center gap-2 text-gray-400">
                          {isUploading ? (
                            <>
                              <Loader2 className="w-6 h-6 animate-spin" />
                              <span>Uploading...</span>
                            </>
                          ) : (
                            <>
                              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              <span>Click to upload image</span>
                              <span className="text-xs text-gray-500">SVG, PNG, JPG or GIF (max. 5MB)</span>
                            </>
                          )}
                        </div>
                      </Label>

                      <div className="relative">
                        <Input
                          value={editingTool.logoUrl || ''}
                          onChange={(e) => {
                            setEditingTool({ ...editingTool, logoUrl: e.target.value });
                          }}
                          placeholder="Or enter image URL..."
                          className="pr-20"
                        />
                        {editingTool.logoUrl && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-1 top-1 h-7"
                            onClick={() => setEditingTool({ ...editingTool, logoUrl: '' })}
                          >
                            Clear
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Image Settings */}
                  <div className="space-y-4">
                    <Label>Image Settings</Label>
                    {editingTool.logoUrl ? (
                      isEditingImage ? (
                        <ImageCropper
                          imageUrl={editingTool.logoUrl}
                          onCropComplete={(originalUrl, settings) => {
                            const updatedSettings: ImageSettings = {
                              size: validateImageSize(settings.size),
                              position: (settings.position as ImagePositionType) || 'center',
                              padding: settings.padding || 0,
                              scale: settings.scale || 1,
                              rotate: settings.rotate || 0,
                              crop: settings.crop ? {
                                ...settings.crop,
                                unit: '%'
                              } : undefined
                            };

                            setEditingTool({
                              ...editingTool,
                              logoUrl: originalUrl,
                              imageSettings: updatedSettings
                            });
                            setIsEditingImage(false);
                          }}
                          initialSettings={editingTool.imageSettings}
                          onCancel={() => setIsEditingImage(false)}
                        />
                      ) : (
                        <Button
                          type="button"
                          variant="outline"
                          className="w-full"
                          onClick={() => setIsEditingImage(true)}
                        >
                          Adjust Image Settings
                        </Button>
                      )
                    ) : (
                      <p className="text-sm text-gray-400 text-center p-4">
                        Upload or provide an image URL to adjust settings
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Label htmlFor="tags">Tags</Label>
                <Input
                  id="tags"
                  value={Array.isArray(editingTool.tags) ? editingTool.tags.join(', ') : editingTool.tags || ''}
                  onChange={(e) => {
                    const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
                    setEditingTool({ ...editingTool, tags });
                  }}
                  placeholder="Enter tags separated by commas"
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditing(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => editingTool && handleSaveEdit(editingTool)}
              disabled={!editingTool || isUploading}
            >
              {isUploading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CombinedToolTracker;