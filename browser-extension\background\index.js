import { initializeApp } from 'firebase/app';
import { getAuth, onAuthStateChanged } from 'firebase/auth';
import { getFirestore, collection, doc, getDoc, setDoc, updateDoc, arrayUnion } from 'firebase/firestore';
import { firebaseConfig } from '../shared/firebase-config.js';

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Intern program UID - the user who will receive automated responses
const INTERN_PROGRAM_UID = 'x8PgDaTjq3Xb4WmAJzOQu0J9Htl2';

// Listen for auth state changes
onAuthStateChanged(auth, (user) => {
  if (user) {
    // User is signed in
    chrome.storage.local.set({
      user: {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL
      }
    });
  } else {
    // User is signed out
    chrome.storage.local.remove('user');
  }
});

// Listen for messages from content scripts or popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'GET_USER') {
    chrome.storage.local.get('user', (data) => {
      sendResponse({ user: data.user || null });
    });
    return true; // Required for async response
  }

  // Handle intern program message
  if (message.type === 'SEND_MESSAGE' && message.data && message.data.text === 'intern') {
    handleInternMessage(message.data);
    return true;
  }
});

// Handle intern program message
async function handleInternMessage(messageData) {
  try {
    // Check if the recipient is the intern program user
    if (messageData.recipientId === INTERN_PROGRAM_UID) {
      const senderId = messageData.senderId;

      // Create automated response with internship information
      const response = {
        text: "Thanks for your interest in our internship program! Here's some information:",
        senderId: INTERN_PROGRAM_UID,
        recipientId: senderId,
        timestamp: new Date().toISOString(),
        read: false,
        buttons: [
          {
            text: "View Internship Details",
            action: "link",
            url: "https://sortmyai.com/internships"
          },
          {
            text: "Apply Now",
            action: "link",
            url: "https://sortmyai.com/internships/apply"
          },
          {
            text: "Schedule a Call",
            action: "link",
            url: "https://calendly.com/sortmyai/internship-call"
          }
        ]
      };

      // Add message to the conversation in Firestore
      const conversationId = [senderId, INTERN_PROGRAM_UID].sort().join('_');
      const conversationRef = doc(db, 'conversations', conversationId);

      // Check if conversation exists
      const conversationDoc = await getDoc(conversationRef);

      if (conversationDoc.exists()) {
        // Add message to existing conversation
        await updateDoc(conversationRef, {
          messages: arrayUnion(response),
          lastMessage: response.text,
          lastMessageTimestamp: response.timestamp,
          updatedAt: response.timestamp
        });
      } else {
        // Create new conversation with the message
        await setDoc(conversationRef, {
          participants: [senderId, INTERN_PROGRAM_UID],
          messages: [response],
          lastMessage: response.text,
          lastMessageTimestamp: response.timestamp,
          createdAt: response.timestamp,
          updatedAt: response.timestamp
        });
      }

      // Notify the user about the new message
      chrome.runtime.sendMessage({
        type: 'NEW_MESSAGE',
        data: response
      });
    }
  } catch (error) {
    console.error('Error handling intern message:', error);
  }
}

// Track when the user visits AI tool websites
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    try {
      const url = new URL(tab.url);
      // This would be expanded with a more comprehensive list of AI tool domains
      const aiToolDomains = [
        'openai.com',
        'midjourney.com',
        'runwayml.com',
        'elevenlabs.io',
        'stability.ai',
        'anthropic.com',
        'huggingface.co'
      ];

      if (aiToolDomains.some(domain => url.hostname.includes(domain))) {
        // Log the visit to the AI tool site
        chrome.storage.local.get('user', (data) => {
          if (data.user) {
            // In a real implementation, you would send this to your backend
            console.log(`User ${data.user.uid} visited ${url.hostname}`);

            // Notify the user about this tool (if appropriate)
            chrome.action.setBadgeText({ text: '!' });
            chrome.action.setBadgeBackgroundColor({ color: '#01AAE9' });

            // Clear the badge after 5 seconds
            setTimeout(() => {
              chrome.action.setBadgeText({ text: '' });
            }, 5000);
          }
        });
      }
    } catch (error) {
      console.error('Error checking AI tool domain:', error);
    }
  }
});