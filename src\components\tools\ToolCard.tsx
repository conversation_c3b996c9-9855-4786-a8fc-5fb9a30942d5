import { Tool, AITool } from '@/types';
import { ToolIcon } from './ToolIcon';

interface ToolCardProps {
  tool: Tool | AITool;
  isUserTool?: boolean;
  onDelete?: (tool: Tool | AITool) => void;
  onAdd?: (tool: Tool | AITool) => void;
}

const ToolCard = ({ tool, isUserTool, onDelete, onAdd }: ToolCardProps) => {
  return (
    <ToolIcon
      tool={tool}
      isUserTool={isUserTool}
      onDelete={onDelete}
      onAdd={onAdd}
    />
  );
};

export default ToolCard;