import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Range, Authorization',
      'Access-Control-Expose-Headers': 'Content-Range, Range', 'Content-Security-Policy': "default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.googleapis.com https://*.google.com https://*.gstatic.com https://cdn.gpteng.co; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: https: http:; connect-src 'self' blob: data: ws: wss: https://*.googleapis.com https://*.google.com https://*.firebaseio.com https://*.firebasestorage.googleapis.com https://storage.googleapis.com https://cdn.gpteng.co https://*.livepeer.studio https://livepeer.studio https://*.lvpr.tv https://*.livepeer.com https://origin.livepeer.com https://*.lp-playback.studio https://vod-cdn.lp-playback.studio https://*.doubleclick.net; frame-src 'self' https://*.google.com https://*.firebaseapp.com https://*.googleapis.com https://*.lvpr.tv https://lvpr.tv; media-src 'self' blob: data: https://*.livepeer.studio https://livepeer.studio https://*.lvpr.tv https://lvpr.tv https://*.livepeer.com https://origin.livepeer.com https://livepeer.com https://*.lp-playback.studio https://vod-cdn.lp-playback.studio https://*.doubleclick.net; manifest-src 'self'; worker-src 'self' blob:;",
      'Cross-Origin-Resource-Policy': 'cross-origin',
      'Cross-Origin-Embedder-Policy': 'credentialless',
      'Cross-Origin-Opener-Policy': 'same-origin-allow-popups'
    }, proxy: {
      '/api/livepeer': {
        target: 'https://livepeer.studio/api',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/livepeer/, ''),
        configure: (proxy) => {
          proxy.on('proxyReq', (proxyReq) => {
            proxyReq.setHeader('Authorization', `Bearer ${process.env.VITE_LIVEPEER_API_KEY}`);
          });
        }
      },
      '/storage.googleapis.com': {
        target: 'https://storage.googleapis.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/storage.googleapis.com/, ''),
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Range'
        }
      },
      '/firebasestorage.googleapis.com': {
        target: 'https://firebasestorage.googleapis.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/firebasestorage.googleapis.com/, ''),
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Range'
        }
      }
    },
    hmr: {
      port: 5173,
      protocol: 'ws'
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  optimizeDeps: {
    exclude: ['firebase', '@firebase/app', '@firebase/firestore'],
    include: ['react', 'react-dom']
  },
  build: {
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          firebase: ['firebase/app', 'firebase/firestore', 'firebase/auth', 'firebase/analytics']
        }
      }
    }
  }
})
